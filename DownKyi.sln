﻿
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DownKyi", "DownKyi\DownKyi.csproj", "{545334D8-F429-47C1-99A7-A25EAC17D76A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DownKyi.Core", "DownKyi.Core\DownKyi.Core.csproj", "{153A9B2C-7494-411C-BA0A-121A1024C08E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{0C40188E-3DE3-4D14-8838-5D2931466DE0}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		CHANGELOG.md = CHANGELOG.md
		cliff.toml = cliff.toml
		Directory.Build.props = Directory.Build.props
		Directory.Build.targets = Directory.Build.targets
		README.md = README.md
		version.txt = version.txt
		Directory.Packages.props = Directory.Packages.props
		LICENSE = LICENSE
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{545334D8-F429-47C1-99A7-A25EAC17D76A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{545334D8-F429-47C1-99A7-A25EAC17D76A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{545334D8-F429-47C1-99A7-A25EAC17D76A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{545334D8-F429-47C1-99A7-A25EAC17D76A}.Release|Any CPU.Build.0 = Release|Any CPU
		{153A9B2C-7494-411C-BA0A-121A1024C08E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{153A9B2C-7494-411C-BA0A-121A1024C08E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{153A9B2C-7494-411C-BA0A-121A1024C08E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{153A9B2C-7494-411C-BA0A-121A1024C08E}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
