# 哔哩下载姬(跨平台版)

<div align="center">

[![GitHub Repo stars](https://img.shields.io/github/stars/yaobiao131/downkyicore)](https://github.com/yaobiao131/downkyicore/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/yaobiao131/downkyicore)](https://github.com/yaobiao131/downkyicore/network)
[![GitHub issues](https://img.shields.io/github/issues/yaobiao131/downkyicore)](https://github.com/yaobiao131/downkyicore/issues)
[![LICENSE](https://img.shields.io/github/license/yaobiao131/downkyicore)](https://github.com/yaobiao131/downkyicore/blob/main/LICENSE)

</div>

## 下载

[![GitHub release (latest by date)](https://img.shields.io/github/v/release/yaobiao131/downkyicore)](https://github.com/yaobiao131/downkyicore/releases/latest)
[![GitHub Release Date](https://img.shields.io/github/release-date/yaobiao131/downkyicore)](https://github.com/yaobiao131/downkyicore/releases/latest)
[![GitHub all releases](https://img.shields.io/github/downloads/yaobiao131/downkyicore/total)](https://github.com/yaobiao131/downkyicore/releases/latest)

[更新日志](CHANGELOG.md)

## 介绍

- 基于[哔哩下载姬Windows版](https://github.com/leiurayer/downkyi)和[AvaloniaUI](https://github.com/AvaloniaUI/Avalonia)制作的跨平台版本(支持Windows、linux、macOS)。
- 开发这个版本目的是由于本人日常使用macOS，当我想下载up视频是偶然间发现了[哔哩下载姬Windows版](https://github.com/leiurayer/downkyi)，发现很好用，就是不能支持macOS使用，就基于AvaloniaUI重新开发了一个跨平台版本。

## 使用说明
- 软件自带.NET6、ffmpeg、aria2运行环境、无需自行安装
- 默认下载路径:
  - Windows: 软件运行目录下的Media文件夹
  - macOS: ~/Library/Application Support/DownKyi/Media
  - linux: ~/.config/DownKyi/Media

## 免责申明
1. 本软件只提供视频解析，不提供任何资源上传、存储到服务器的功能。
2. 本软件仅解析来自B站的内容，不会对解析到的音视频进行二次编码，部分视频会进行有限的格式转换、拼接等操作。
3. 本软件解析得到的所有内容均来自B站UP主上传、分享，其版权均归原作者所有。内容提供者、上传者(UP主)应对其提供、上传的内容承担全部责任。
4. **本软件提供的所有内容，仅可用作学习交流使用，未经原作者授权，禁止用于其他用途。请在下载24小时内删除。为尊重作者版权，请前往资源的原始发布网站观看，支持原创，谢谢。**
5. 因使用本软件产生的版权问题，软件作者概不负责。