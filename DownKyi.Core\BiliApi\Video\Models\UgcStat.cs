﻿using DownKyi.Core.BiliApi.Models;
using Newtonsoft.Json;

namespace DownKyi.Core.BiliApi.Video.Models;

public class UgcStat : BaseModel
{
    [JsonProperty("season_id")] public long SeasonId { get; set; }
    [JsonProperty("view")] public long View { get; set; }
    [<PERSON><PERSON><PERSON>roperty("danmaku")] public long Dan<PERSON>ku { get; set; }
    [<PERSON><PERSON><PERSON>roperty("reply")] public long Reply { get; set; }
    [JsonProperty("fav")] public long Favorite { get; set; }
    [JsonProperty("coin")] public long Coin { get; set; }
    [JsonProperty("share")] public long Share { get; set; }
    [JsonProperty("now_rank")] public long NowRank { get; set; }
    [Json<PERSON>roperty("his_rank")] public long HisRank { get; set; }
    [JsonProperty("like")] public long Like { get; set; }
}