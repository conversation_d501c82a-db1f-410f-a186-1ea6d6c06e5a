<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Color x:Key="ColorWindowBorder">Black</Color>
    <Color x:Key="ColorCaptionForeground">white</Color>
    <Color x:Key="ColorCaptionBackground">#7FD0D0D0</Color>
    <Color x:Key="ColorSystemBtnTint">white</Color>
    <Color x:Key="ColorSystemBtnTintDark">black</Color>
    <Color x:Key="ColorSystemBtnHover">#FF6ECEF8</Color>
    <Color x:Key="ColorSystemBtnPressed">#FF30B8F6</Color>
    <Color x:Key="ColorCloseBtnHover">#FFF1707A</Color>
    <Color x:Key="ColorCloseBtnPressed">#FFE81123</Color>
    <Color x:Key="ColorPrimary">#FF00A1D6</Color>
    <Color x:Key="ColorForeground">#FF00A1D6</Color>
    <Color x:Key="ColorBackground">#28282800</Color>
    <Color x:Key="ColorForegroundDark">#FFFFFFFF</Color>
    <Color x:Key="ColorBackgroundDark">#FF00A1D6</Color>
    <Color x:Key="ColorImageBorder">#7FD0D0D0</Color>
    <Color x:Key="ColorImageBackground">#7FD0D0D0</Color>
    <Color x:Key="ColorText">Black</Color>
    <Color x:Key="ColorTextDark">white</Color>
    <Color x:Key="ColorTabHeaderGrey">#FF141414</Color>
    
    <Color x:Key="ColorDataGridHighlight">#096085</Color>

</ResourceDictionary>
