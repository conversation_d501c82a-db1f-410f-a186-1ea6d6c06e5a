﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="DownKyi.Views.ViewMyHistory"
             xmlns:prism="http://prismlibrary.com/"
             prism:ViewModelLocator.AutoWireViewModel="True"
             xmlns:vm="clr-namespace:DownKyi.ViewModels"
             xmlns:vmp="clr-namespace:DownKyi.ViewModels.PageViewModels"
             xmlns:custom="clr-namespace:DownKyi.CustomControl"
             x:DataType="vm:ViewMyHistoryViewModel"
             xmlns:asyncImageLoader="clr-namespace:DownKyi.CustomControl.AsyncImageLoader"
             xmlns:customAction="clr-namespace:DownKyi.CustomAction">
    <UserControl.Resources>
        <ControlTheme x:Key="MediaListStyle" TargetType="{x:Type ListBoxItem}" x:DataType="vmp:HistoryMedia">
            <Setter Property="IsSelected" Value="{Binding IsSelected}" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ListBoxItem}">
                        <Grid
                            Name="nameMediaPanel"
                            Height="100"
                            Margin="20,15,10,5" ColumnDefinitions="160,*">

                            <Interaction.Behaviors>
                                <DataTriggerBehavior Binding="{Binding IsSelected}" Value="True">
                                    <ChangePropertyAction TargetObject="nameChecked" PropertyName="IsVisible"
                                                          Value="true" />
                                </DataTriggerBehavior>
                                <DataTriggerBehavior Binding="{Binding IsSelected}" Value="False">
                                    <ChangePropertyAction TargetObject="nameChecked" PropertyName="IsVisible"
                                                          Value="false" />
                                </DataTriggerBehavior>
                            </Interaction.Behaviors>

                            <Border
                                Name="nameCover"
                                Grid.Column="0"
                                Width="160"
                                Height="100"
                                HorizontalAlignment="Center"
                                CornerRadius="5">
                                <Border.Background>
                                    <!--  长宽比：1.6  -->
                                    <ImageBrush asyncImageLoader:ImageBrushLoader.Source="{Binding Cover}"
                                                asyncImageLoader:ImageBrushLoader.Width="160"
                                                asyncImageLoader:ImageBrushLoader.Height="100"
                                                Stretch="UniformToFill" />
                                </Border.Background>
                            </Border>

                            <Image
                                Name="nameChecked"
                                Grid.Column="0"
                                Width="24"
                                Height="24"
                                Margin="10"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top"
                                Source="/Resources/checked.png" />

                            <Grid Grid.Column="1" Margin="20,0,0,5" RowDefinitions="*,*,*">

                                <TextBlock
                                    Name="nameTitle"
                                    Grid.Row="0"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Top"
                                    Cursor="Hand"
                                    FontSize="14"
                                    FontWeight="Bold"
                                    Text="{Binding Title}"
                                    TextTrimming="CharacterEllipsis"
                                    ToolTip.Tip="{Binding Title}">
                                    <Interaction.Behaviors>
                                        <EventTriggerBehavior EventName="PointerReleased">
                                            <InvokeCommandAction Command="{Binding TitleCommand}"
                                                                 CommandParameter="{ReflectionBinding DataContext.PageName, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=UserControl}}" />
                                        </EventTriggerBehavior>
                                    </Interaction.Behaviors>
                                </TextBlock>

                                <TextBlock
                                    Grid.Row="1"
                                    FontSize="12"
                                    Foreground="{DynamicResource BrushTextGrey}"
                                    Text="{Binding SubTitle}" />

                                <Grid Grid.Row="2" ColumnDefinitions="300,*">
                                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                                        <ContentControl
                                            Width="16"
                                            Height="16"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center">
                                            <Path
                                                Width="{Binding Platform.Width}"
                                                Height="{Binding Platform.Height}"
                                                Data="{Binding Platform.Data}"
                                                Fill="{DynamicResource BrushTextGrey}"
                                                Stretch="Uniform" />
                                        </ContentControl>

                                        <TextBlock
                                            Margin="10,0,0,0"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            FontSize="12"
                                            Foreground="{DynamicResource BrushTextGrey}"
                                            Text="{Binding Progress}" />
                                        <StackPanel Orientation="Horizontal" IsVisible="{Binding PartdescVisibility}">
                                            <TextBlock
                                                Margin="10,0,0,0"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushTextGrey}"
                                                Text="|" />
                                            <TextBlock
                                                Margin="10,0,0,0"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushTextGrey}"
                                                Text="{Binding Partdesc}" />
                                        </StackPanel>
                                    </StackPanel>

                                    <StackPanel
                                        Grid.Column="1"
                                        Orientation="Horizontal"
                                        IsVisible="{Binding UpAndTagVisibility}">
                                        <StackPanel
                                            Name="nameUpper"
                                            Cursor="Hand"
                                            Orientation="Horizontal">
                                            <Interaction.Behaviors>
                                                <EventTriggerBehavior EventName="PointerReleased">
                                                    <InvokeCommandAction Command="{Binding UpCommand}"
                                                                         CommandParameter="{ReflectionBinding DataContext.PageName, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=UserControl}}" />
                                                </EventTriggerBehavior>
                                            </Interaction.Behaviors>
                                            <Border
                                                Name="nameUpperHeader"
                                                Width="24"
                                                Height="24"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                CornerRadius="12">
                                                <Border.Background>
                                                    <ImageBrush
                                                        asyncImageLoader:ImageBrushLoader.Source="{Binding UpHeader}"
                                                        asyncImageLoader:ImageBrushLoader.Width="24"
                                                        asyncImageLoader:ImageBrushLoader.Height="24" />
                                                </Border.Background>
                                            </Border>
                                            <TextBlock
                                                Name="nameUpperName"
                                                Margin="10,0,0,0"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushTextGrey}"
                                                Text="{Binding UpName}" />
                                        </StackPanel>
                                        <TextBlock
                                            Margin="10,0,0,0"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            FontSize="12"
                                            Foreground="{DynamicResource BrushTextGrey}"
                                            Text="|" />
                                        <TextBlock
                                            Margin="10,0,0,0"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            FontSize="12"
                                            Foreground="{DynamicResource BrushTextGrey}"
                                            Text="{Binding TagName}" />
                                    </StackPanel>
                                </Grid>
                            </Grid>

                            <TextBlock
                                Grid.Column="1"
                                Height="1"
                                Margin="20,0,20,0"
                                VerticalAlignment="Bottom"
                                Background="{DynamicResource BrushBorderTranslucent}" />
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style Selector="^ /template/ TextBlock#nameTitle:pointerover">
                <Setter Property="Foreground" Value="#FF00A1D6" />
            </Style>
            <Style Selector="^ /template/ TextBlock#nameUpperName:pointerover">
                <Setter Property="Foreground" Value="#FF00A1D6" />
            </Style>
        </ControlTheme>
    </UserControl.Resources>
    <Grid RowDefinitions="50,1,*">
        <Grid Grid.Row="0" ColumnDefinitions="100,*,100">
            <Button
                Grid.Column="0"
                Margin="10,5,0,5"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Command="{Binding BackSpaceCommand}"
                Theme="{StaticResource ImageBtnStyle}">
                <StackPanel Orientation="Horizontal">
                    <ContentControl Width="24" Height="24">
                        <Path
                            Width="{Binding ArrowBack.Width}"
                            Height="{Binding ArrowBack.Height}"
                            Data="{Binding ArrowBack.Data}"
                            Fill="{Binding ArrowBack.Fill}"
                            Stretch="None" />
                    </ContentControl>
                    <TextBlock
                        VerticalAlignment="Center"
                        FontSize="16"
                        Foreground="{DynamicResource BrushTextDark}"
                        Text="{DynamicResource MyHistory}" />
                </StackPanel>
            </Button>

            <Button
                Grid.Column="2"
                Width="24"
                Height="24"
                Margin="10,5"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Command="{Binding DownloadManagerCommand}"
                Theme="{StaticResource ImageBtnStyle}"
                ToolTip.Tip="{DynamicResource DownloadManager}">
                <ContentControl>
                    <Path
                        Width="{Binding DownloadManage.Width}"
                        Height="{Binding DownloadManage.Height}"
                        Data="{Binding DownloadManage.Data}"
                        Fill="{Binding DownloadManage.Fill}"
                        Stretch="Uniform" />
                </ContentControl>
            </Button>
        </Grid>

        <TextBlock Grid.Row="1" Background="{DynamicResource BrushBorder}" />

        <Grid Grid.Row="2" IsVisible="{Binding ContentVisibility}" RowDefinitions="*,1,50">
            <ListBox
                ItemsSource="{Binding Medias}"
                x:Name="NameMedias"
                Grid.Row="0"
                BorderThickness="0"
                ItemContainerTheme="{StaticResource MediaListStyle}"
                ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                SelectionMode="Multiple">
                <Interaction.Behaviors>
                    <customAction:InfiniteScrollBehavior

                        LoadMoreCommand="{Binding LoadMoreCommand}">

                    </customAction:InfiniteScrollBehavior>

                    <EventTriggerBehavior EventName="SelectionChanged">
                        <InvokeCommandAction Command="{Binding MediasCommand}"
                                             CommandParameter="{Binding ElementName=NameMedias, Path=SelectedItems}" />
                    </EventTriggerBehavior>
                </Interaction.Behaviors>
                <ListBox.ItemsPanel>
                    <ItemsPanelTemplate>
                        <VirtualizingStackPanel />
                    </ItemsPanelTemplate>
                </ListBox.ItemsPanel>
                <ListBox.Theme>
                    <ControlTheme TargetType="{x:Type ListBox}">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="{x:Type ListBox}">
                                    <Border
                                        x:Name="Bd"
                                        Padding="0"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}">
                                        <ScrollViewer Focusable="False">
                                            <ItemsPresenter ItemsPanel="{TemplateBinding ItemsPanel}" />
                                        </ScrollViewer>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </ControlTheme>
                </ListBox.Theme>
            </ListBox>

            <TextBlock Grid.Row="1" Background="{DynamicResource BrushBorder}" />

            <Grid Grid.Row="2" ColumnDefinitions="80,*,100,90">
                <CheckBox
                    Grid.Column="0"
                    Margin="10,0,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Command="{Binding SelectAllCommand}"
                    CommandParameter="{Binding ElementName=NameMedias, Path=SelectedItem}"
                    Content="{DynamicResource SelectAll}"
                    Foreground="{DynamicResource BrushTextDark}"
                    IsChecked="{Binding IsSelectAll, Mode=TwoWay}" />

                <Button
                    Grid.Column="2"
                    Margin="0,0,10,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Command="{Binding AddToDownloadCommand}"
                    Content="{DynamicResource DownloadSelectedPublication}"
                    FontSize="12"
                    Foreground="{DynamicResource BrushText}"
                    Theme="{StaticResource BtnStyle}" />

                <Button
                    Grid.Column="3"
                    Margin="0,0,10,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Command="{Binding AddAllToDownloadCommand}"
                    Content="{DynamicResource DownloadAllPublication}"
                    FontSize="12"
                    Foreground="{DynamicResource BrushText}"
                    Theme="{StaticResource BtnStyle}" />
            </Grid>
        </Grid>

        <!--  加载gif  -->
        <StackPanel
            Grid.Row="2"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Orientation="Vertical"
            IsVisible="{Binding LoadingVisibility}">
            <custom:Loading
                Width="40"
                Height="40"
                Foreground="Gray"
                IsActive="{Binding Loading}" />
            <TextBlock
                Margin="0,10,0,0"
                FontSize="14"
                Foreground="{DynamicResource BrushTextDark}"
                Text="{DynamicResource PublicationWait}" />
        </StackPanel>

        <!--  没有数据提示  -->
        <Image
            Grid.Row="2"
            Width="256"
            Height="256"
            Source="/Resources/no-data.png"
            IsVisible="{Binding NoDataVisibility}" />
    </Grid>
</UserControl>