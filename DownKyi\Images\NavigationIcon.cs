namespace DownKyi.Images;

public class NavigationIcon
{
    private static NavigationIcon? _instance;
    public static NavigationIcon Instance()
    {
        return _instance ??= new NavigationIcon();
    }

    public NavigationIcon()
    {
        ArrowBack = new VectorImage
        {
            Height = 24,
            Width = 24,
            Data = @"M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77
                 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z",
            Fill = "#FF000000"
        };

        Logout = new VectorImage
        {
            Height = 18.75,//100,
            Width = 24,//128,
            Data = @"M21.5,11.3l-2.8-2.7c-0.2-0.2-0.2-0.4-0.2-0.7c0.1-0.3,0.2-0.4,0.5-0.5c0.3-0.1,0.5,0,0.7,0.2l4,3.9
                 c0.1,0.1,0.2,0.3,0.2,0.5c0,0.2-0.1,0.4-0.2,0.5l-4,3.9c-0.2,0.2-0.4,0.2-0.7,0.2c-0.3-0.1-0.4-0.2-0.5-0.5c-0.1-0.3,0-0.5,0.2-0.7
                 l2.8-2.7h-8.7c-0.2,0-0.4-0.1-0.5-0.2c-0.1-0.1-0.2-0.3-0.2-0.5c0-0.2,0.1-0.4,0.2-0.5c0.1-0.1,0.3-0.2,0.5-0.2
                 C12.8,11.3,21.5,11.3,21.5,11.3z M16,18.2c0-0.2,0.1-0.4,0.2-0.5c0.1-0.1,0.3-0.2,0.5-0.2c0.2,0,0.4,0.1,0.5,0.2
                 c0.1,0.1,0.2,0.3,0.2,0.5v0.4c0,0.8-0.3,1.5-0.9,2c-0.6,0.5-1.2,0.8-2,0.8H2.9c-0.8,0-1.5-0.3-2-0.8S0,19.3,0,18.5V5.4
                 c0-0.8,0.3-1.5,0.8-2s1.2-0.8,2-0.8h11.7c0.8,0,1.5,0.3,2,0.8c0.5,0.5,0.8,1.2,0.8,2v0.4c0,0.3-0.1,0.5-0.4,0.6
                 c-0.2,0.1-0.5,0.1-0.7,0C16.1,6.3,16,6.1,16,5.8V5.4c0-0.4-0.1-0.7-0.4-1c-0.3-0.3-0.6-0.4-1-0.4H2.9c-0.4,0-0.7,0.1-1,0.4
                 C1.6,4.7,1.4,5,1.4,5.4v13.1c0,0.4,0.2,0.7,0.4,1c0.3,0.3,0.6,0.4,1,0.4h11.7c0.4,0,0.7-0.1,1-0.4c0.3-0.3,0.4-0.6,0.4-1L16,18.2
                 L16,18.2z",
            Fill = "#FF000000"
        };
    }

    public VectorImage ArrowBack { get; private set; }
    public VectorImage Logout { get; private set; }

}