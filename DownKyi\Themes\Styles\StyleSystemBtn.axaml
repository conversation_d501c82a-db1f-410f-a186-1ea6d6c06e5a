<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="SystemBtnStyle" TargetType="{x:<PERSON> Button}">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="BorderBrush" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <ContentPresenter
                            Name="content"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding Content}" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style Selector="^:pointerover">
            <Setter Property="Background" Value="{DynamicResource BrushSystemBtnHover}" />
        </Style>
        <Style Selector="^:pressed">
            <Setter Property="Background" Value="{DynamicResource BrushSystemBtnPressed}" />
        </Style>
    </ControlTheme>

    <ControlTheme
        x:Key="CloseBtnStyle"
        BasedOn="{StaticResource SystemBtnStyle}"
        TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <ContentPresenter
                            Name="content"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding Content}" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style Selector="^:pointerover">
            <Setter Property="Background" Value="{DynamicResource BrushCloseBtnHover}" />
        </Style>
        <Style Selector="^:pressed">
            <Setter Property="Background" Value="{DynamicResource BrushCloseBtnPressed}" />
        </Style>
    </ControlTheme>

</ResourceDictionary>