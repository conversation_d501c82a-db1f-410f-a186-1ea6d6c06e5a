﻿using Newtonsoft.Json;

namespace DownKyi.Core.Aria2cNet.Client.Entity;

[JsonObject]
public class SystemListNotifications
{
    [JsonProperty("id")] public string Id { get; set; }

    [JsonProperty("jsonrpc")] public string Jsonrpc { get; set; }

    [JsonProperty("result")] public List<string> Result { get; set; }

    [JsonProperty("error")] public AriaError Error { get; set; }

    public override string ToString()
    {
        return JsonConvert.SerializeObject(this);
    }
}