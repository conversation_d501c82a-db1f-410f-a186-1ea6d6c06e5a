﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="DownKyi.Views.Settings.ViewVideo"
             xmlns:prism="http://prismlibrary.com/"
             xmlns:vms="clr-namespace:DownKyi.ViewModels.Settings"
             prism:ViewModelLocator.AutoWireViewModel="True"
             x:DataType="vms:ViewVideoViewModel">
    <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="50,0" Orientation="Vertical">

            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock FontSize="18" Text="{DynamicResource Video}" />
            </StackPanel>
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource FirstVideoCodecs}" />
                <ComboBox
                    Name="NameVideoCodecs"
                    DisplayMemberBinding="{ReflectionBinding Name}"
                    Width="120"
                    VerticalContentAlignment="Center"
                    ItemsSource="{Binding VideoCodecs}"
                    SelectedValue="{Binding SelectedVideoCodec}">
                    <Interaction.Behaviors>
                        <EventTriggerBehavior EventName="SelectionChanged">
                            <InvokeCommandAction Command="{Binding VideoCodecsCommand}"
                                                 CommandParameter="{Binding ElementName=NameVideoCodecs,Path=SelectedValue}" />
                        </EventTriggerBehavior>
                    </Interaction.Behaviors>
                </ComboBox>
            </StackPanel>
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource FirstVideoQuality}" />
                <ComboBox
                    Name="NameVideoQualityList"
                    Width="120"
                    VerticalContentAlignment="Center"
                    DisplayMemberBinding="{ReflectionBinding Name}"
                    ItemsSource="{Binding VideoQualityList}"
                    SelectedItem="{Binding SelectedVideoQuality}">
                    <Interaction.Behaviors>
                        <EventTriggerBehavior EventName="SelectionChanged">
                            <InvokeCommandAction Command="{Binding VideoQualityCommand}"
                                                 CommandParameter="{Binding ElementName=NameVideoQualityList,Path=SelectedValue}" />
                        </EventTriggerBehavior>
                    </Interaction.Behaviors>
                </ComboBox>
            </StackPanel>
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource FirstAudioQuality}" />
                <ComboBox
                    Name="NameAudioQualityList"
                    Width="120"
                    VerticalContentAlignment="Center"
                    DisplayMemberBinding="{ReflectionBinding Name}"
                    ItemsSource="{Binding AudioQualityList}"
                    SelectedItem="{Binding SelectedAudioQuality}">
                    <Interaction.Behaviors>
                        <EventTriggerBehavior EventName="SelectionChanged">
                            <InvokeCommandAction Command="{Binding AudioQualityCommand}"
                                                 CommandParameter="{Binding ElementName=NameAudioQualityList,Path=SelectedValue}" />
                        </EventTriggerBehavior>
                    </Interaction.Behaviors>
                </ComboBox>
            </StackPanel>
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource VideoParseType}" />
                <ComboBox
                    Name="NameVideoParseTypeList"
                    Width="200"
                    VerticalContentAlignment="Center"
                    DisplayMemberBinding="{Binding Name}"
                    ItemsSource="{Binding VideoParseTypeList}"
                    SelectedItem="{Binding SelectedVideoParseType}">
                    <Interaction.Behaviors>
                        <EventTriggerBehavior EventName="SelectionChanged">
                            <InvokeCommandAction Command="{Binding VideoParseTypeCommand}"
                                                 CommandParameter="{Binding ElementName=NameVideoParseTypeList,Path=SelectedValue}" />
                        </EventTriggerBehavior>
                    </Interaction.Behaviors>
                </ComboBox>
            </StackPanel>
            <CheckBox
                Margin="0,20,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Command="{Binding IsTranscodingFlvToMp4Command}"
                Content="{DynamicResource IsTranscodingFlvToMp4}"
                FontSize="12"
                Foreground="{DynamicResource BrushTextDark}"
                IsChecked="{Binding IsTranscodingFlvToMp4, Mode=TwoWay}" />

            <CheckBox
                Margin="0,20,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Command="{Binding IsTranscodingAacToMp3Command}"
                Content="{DynamicResource IsTranscodingAacToMp3}"
                FontSize="12"
                Foreground="{DynamicResource BrushTextDark}"
                IsChecked="{Binding IsTranscodingAacToMp3, Mode=TwoWay}" />

            <TextBlock
                Height="1"
                Margin="0,20,0,0"
                Background="{DynamicResource BrushBorder}" />

            <CheckBox
                Name="NameIsUseDefaultDirectory"
                Margin="0,20,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Command="{Binding IsUseDefaultDirectoryCommand}"
                Content="{DynamicResource IsUseDefaultDirectory}"
                FontSize="12"
                Foreground="{DynamicResource BrushTextDark}"
                IsChecked="{Binding IsUseDefaultDirectory, Mode=TwoWay}" />

            <StackPanel
                Margin="0,20,0,0"
                Orientation="Vertical"
                IsVisible="{Binding ElementName=NameIsUseDefaultDirectory,Path=IsChecked}">

                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        VerticalAlignment="Center"
                        FontSize="12"
                        Foreground="{DynamicResource BrushTextDark}"
                        Text="{DynamicResource DefaultDirectory}" />
                    <TextBlock
                        Margin="10,0,0,0"
                        VerticalAlignment="Center"
                        FontSize="12"
                        Foreground="{DynamicResource BrushTextGrey}"
                        Text="{DynamicResource DefaultDirectoryTip}" />
                </StackPanel>
                <StackPanel Margin="0,10,0,0" Orientation="Horizontal">
                    <TextBlock
                        MaxWidth="600"
                        VerticalAlignment="Center"
                        FontSize="12"
                        Foreground="{DynamicResource BrushTextDark}"
                        Text="{Binding SaveVideoDirectory}"
                        TextTrimming="CharacterEllipsis"
                        ToolTip.Tip="{Binding SaveVideoDirectory}" />
                    <Button
                        Width="75"
                        Margin="30,0,0,0"
                        Command="{Binding ChangeSaveVideoDirectoryCommand}"
                        Content="{DynamicResource ChangeDirectory}"
                        FontSize="12" />
                </StackPanel>
            </StackPanel>
            
            <StackPanel
                Margin="0,20,0,0"
                Orientation="Horizontal" >
                <CheckBox
                    Command="{Binding GenerateMovieMetadataCommand }"
                    IsChecked="{Binding GenerateMovieMetadata}"
                    Content="{DynamicResource GenerateVideoMetadata}"
                    FontSize="12"
                    ToolTip.Tip="nfo"
                    Foreground="{DynamicResource BrushTextDark}"/>
            </StackPanel>

            <HeaderedContentControl
                Header="{DynamicResource DownloadContent2}"
                Background="LightGray"
                Margin="0,20,0,0"
                Foreground="{DynamicResource BrushTextDark}"
                CornerRadius="10"
                FontSize="12">
                    <StackPanel Margin="10" Orientation="Horizontal">
                    <CheckBox
                        Margin="0,0,0,0"
                        Command="{Binding DownloadAllCommand}"
                        Content="{DynamicResource DownloadAll}"
                        Foreground="{DynamicResource BrushTextDark}"
                        IsChecked="{Binding DownloadAll, Mode=TwoWay}" />
                    <CheckBox
                        Margin="40,0,0,0"
                        Command="{Binding DownloadAudioCommand}"
                        Content="{DynamicResource DownloadAudio}"
                        Foreground="{DynamicResource BrushTextDark}"
                        IsChecked="{Binding DownloadAudio, Mode=TwoWay}" />
                    <CheckBox
                        Margin="40,0,0,0"
                        Command="{Binding DownloadVideoCommand}"
                        Content="{DynamicResource DownloadVideo}"
                        Foreground="{DynamicResource BrushTextDark}"
                        IsChecked="{Binding DownloadVideo, Mode=TwoWay}" />
                    <CheckBox
                        Margin="40,0,0,0"
                        Command="{Binding DownloadDanmakuCommand}"
                        Content="{DynamicResource DownloadDanmaku}"
                        Foreground="{DynamicResource BrushTextDark}"
                        IsChecked="{Binding DownloadDanmaku, Mode=TwoWay}" />
                    <CheckBox
                        Margin="40,0,0,0"
                        Command="{Binding DownloadSubtitleCommand}"
                        Content="{DynamicResource DownloadSubtitle}"
                        Foreground="{DynamicResource BrushTextDark}"
                        IsChecked="{Binding DownloadSubtitle, Mode=TwoWay}" />
                    <CheckBox
                        Margin="40,0,0,0"
                        Command="{Binding DownloadCoverCommand}"
                        Content="{DynamicResource DownloadCover}"
                        Foreground="{DynamicResource BrushTextDark}"
                        IsChecked="{Binding DownloadCover, Mode=TwoWay}" />
                </StackPanel>
            </HeaderedContentControl>
            
            <HeaderedContentControl
                Header="{DynamicResource FileNameParts}"
                Background="LightGray"
                Margin="0,20,0,0"
                Foreground="{DynamicResource BrushTextDark}"
                CornerRadius="10"
                FontSize="12">
                <Grid ColumnDefinitions="100,*" RowDefinitions="*,*,*" Margin="10">
                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="0"
                        FontSize="12"
                        Foreground="{DynamicResource BrushTextDark}"
                        Text="{DynamicResource OptionalFields}" />
                    <ListBox
                        Name="NameOptionalFields"
                        Grid.Row="0"
                        Grid.Column="1"
                        Margin="0,0,0,20"
                        ItemContainerTheme="{StaticResource TagItem2Style}"
                        ItemsSource="{Binding OptionalFields, Mode=TwoWay}"
                        SelectedIndex="{Binding SelectedOptionalField}"
                        SelectionMode="Single"
                        Theme="{StaticResource Tag2Style}">
                        <Interaction.Behaviors>
                            <EventTriggerBehavior EventName="SelectionChanged">
                                <InvokeCommandAction Command="{Binding OptionalFieldsCommand}"
                                                     CommandParameter="{Binding ElementName=NameOptionalFields, Path=SelectedItem}" />
                            </EventTriggerBehavior>
                        </Interaction.Behaviors>
                    </ListBox>

                    <TextBlock
                        Grid.Row="1"
                        Grid.Column="0"
                        FontSize="12"
                        Foreground="{DynamicResource BrushTextDark}"
                        Text="{DynamicResource FileName}" />
                    <ListBox
                        x:Name="NameSelectedFileName"
                        Grid.Row="1"
                        Grid.Column="1"
                        MinHeight="30"
                        ItemsSource="{Binding SelectedFileName, Mode=TwoWay}"
                        SelectionMode="Single"
                        Theme="{StaticResource Tag2Style}">
                        <Interaction.Behaviors>
                            <EventTriggerBehavior EventName="DoubleTapped">
                                <InvokeCommandAction Command="{Binding SelectedFileNameRightCommand}"
                                                     CommandParameter="{Binding ElementName=NameSelectedFileName, Path=SelectedItem}" />
                            </EventTriggerBehavior>
                        </Interaction.Behaviors>
                        <ListBox.ItemContainerTheme>
                            <ControlTheme BasedOn="{StaticResource TagItem2Style}" TargetType="{x:Type ListBoxItem}">
                                <Setter Property="DragDrop.AllowDrop" Value="True" />
                                <Setter Property="(Interaction.Behaviors)">
                                    <BehaviorCollectionTemplate>
                                        <BehaviorCollection>
                                            <ItemDragBehavior Orientation="Horizontal" />
                                        </BehaviorCollection>
                                    </BehaviorCollectionTemplate>
                                </Setter>
                            </ControlTheme>
                        </ListBox.ItemContainerTheme>
                    </ListBox>

                    <Grid
                        Grid.Row="2"
                        Grid.Column="1"
                        Margin="0,20,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <StackPanel
                            Grid.Column="0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Bottom"
                            Orientation="Horizontal">
                            <TextBlock
                                VerticalAlignment="Center"
                                FontSize="12"
                                Foreground="{DynamicResource BrushTextDark}"
                                Text="{DynamicResource FileNameTimeFormat}" />
                            <ComboBox
                                Name="NameFileNamePartTimeFormat"
                                Width="120"
                                VerticalAlignment="Center"
                                VerticalContentAlignment="Center"
                                ItemsSource="{Binding FileNamePartTimeFormatList}"
                                SelectedItem="{Binding SelectedFileNamePartTimeFormat}">
                                <Interaction.Behaviors>
                                    <EventTriggerBehavior EventName="SelectionChanged">
                                        <InvokeCommandAction Command="{Binding FileNamePartTimeFormatCommand}"
                                                             CommandParameter="{Binding ElementName=NameFileNamePartTimeFormat, Path=SelectedItem}" />
                                    </EventTriggerBehavior>
                                </Interaction.Behaviors>
                            </ComboBox>
                        </StackPanel>
                        <StackPanel
                            Grid.Column="1"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Bottom"
                            Orientation="Horizontal">
                            <TextBlock
                                VerticalAlignment="Center"
                                FontSize="12"
                                Foreground="{DynamicResource BrushTextDark}"
                                Text="{DynamicResource OrderFormat}" />
                            <ComboBox
                                Name="NameOrderFormat"
                                Width="120"
                                VerticalAlignment="Center"
                                VerticalContentAlignment="Center"
                                DisplayMemberBinding="{Binding Name}"
                                ItemsSource="{Binding OrderFormatList}"
                                SelectedItem="{Binding OrderFormatDisplay}">
                                <Interaction.Behaviors>
                                    <EventTriggerBehavior EventName="SelectionChanged">
                                        <InvokeCommandAction Command="{Binding OrderFormatCommand}"
                                                             CommandParameter="{Binding ElementName=NameOrderFormat, Path=SelectedItem}" />
                                    </EventTriggerBehavior>
                                </Interaction.Behaviors>
                            </ComboBox>
                        </StackPanel>
                        <Button
                            Grid.Column="2"
                            Width="75"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Command="{Binding ResetCommand}"
                            Content="{DynamicResource Reset}"
                            FontSize="12"
                            Theme="{StaticResource BtnBorderStyle}" />
                    </Grid>

                </Grid>
            </HeaderedContentControl>
        </StackPanel>
    </ScrollViewer>
</UserControl>