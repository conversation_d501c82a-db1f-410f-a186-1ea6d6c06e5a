AppBaseName = DownKyi
AppFriendlyName = 哔哩下载姬
AppId = cn.bzdrs.downkyi
AppVersionRelease = ${APP_VERSION}
AppShortSummary = 哔哩下载姬，哔哩哔哩网站视频下载工具，支持批量下载，支持8K、HDR、杜比视界，提供工具箱（音视频提取、去水印等）

AppLicenseId = GPL-3.0-or-later
AppLicenseFile = ../../LICENSE

IconFiles = """
    icons/logo.icns
    icons/logo.16.png
    icons/logo.32.png
    icons/logo.64.png
    icons/logo.128.png
    icons/logo.256.png
    icons/logo.512.png
    icons/logo.1024.png
"""
DesktopFile = app.desktop
# PUBLISHER
PublisherName = yaobiao131
PublisherCopyright = Copyright (C) yaobiao131 2024
PublisherLinkName = Home Page
PublisherLinkUrl = https://github.com/yaobiao131/downkyicore
PublisherEmail = <EMAIL>
DotnetProjectPath = ../../

DesktopNoDisplay = false
DesktopTerminal = false

# Package Out
OutputDirectory = output/
PackageName = DownKyi
PrimeCategory = Utility

# AppImage
AppImageVersionOutput = true

DebianRecommends = """
    libc6
    libgcc1
    libgcc-s1
    libgssapi-krb5-2
    libicu
    libssl
    libstdc++6
    libunwind
    zlib1g
"""

# FLATPAK OPTIONS
FlatpakPlatformRuntime = org.freedesktop.Platform
FlatpakPlatformSdk = org.freedesktop.Sdk
FlatpakPlatformVersion = 22.08
; FlatpakFinishArgs = """
;     --socket=wayland
;     --socket=x11
;     --filesystem=host
;     --share=network
; """
; FlatpakBuilderArgs = 

DotnetPublishArgs = -p:Version=${APP_VERSION} --self-contained true -p:DebugType=None -p:DebugSymbols=false

# WINDOWS SETUP OPTIONS
SetupAdminInstall = true
SetupCommandPrompt = PupNet Console
SetupMinWindowsVersion = 6.1sp1
SetupSignTool = 
SetupSuffixOutput = 
SetupVersionOutput = true