﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <DrawingImage x:Key="CheckBox.UncheckedDrawingImage">
        <DrawingImage.Drawing>
            <DrawingGroup ClipGeometry="M0,0 V1024 H1024 V0 H0 Z">
                <DrawingGroup.Transform>
                    <TranslateTransform X="4.2632564145606011E-14" Y="4.2632564145606011E-14" />
                </DrawingGroup.Transform>
                <GeometryDrawing Brush="#FF000000"
                                 Geometry="F1 M1024,1024z M0,0z M170.67,85.33Q134.67,86.67 110.67,110.67 86.67,134.67 85.33,170.67L85.33,853.33Q86.67,889.33 110.67,913.33 134.67,937.33 170.67,938.67L853.33,938.67Q889.33,937.33 913.33,913.33 937.33,889.33 938.67,853.33L938.67,170.67Q937.33,134.67 913.33,110.67 889.33,86.67 853.33,85.33L170.67,85.33z M170.67,0L853.33,0Q925.33,1.33 974,50 1022.67,98.67 1024,170.67L1024,853.33Q1022.67,925.33 974,974 925.33,1022.67 853.33,1024L170.67,1024Q98.67,1022.67 50,974 1.32999999999996,925.33 -4.2632564145606E-14,853.33L-4.2632564145606E-14,170.67Q1.32999999999996,98.67 50,50 98.67,1.32999999999996 170.67,-4.2632564145606E-14z" />
            </DrawingGroup>
        </DrawingImage.Drawing>
    </DrawingImage>

    <DrawingImage x:Key="CheckBox.CheckedDrawingImage">
        <DrawingImage.Drawing>
            <DrawingGroup ClipGeometry="M0,0 V1024 H1024 V0 H0 Z">
                <DrawingGroup.Transform>
                    <TranslateTransform X="4.2632564145606011E-14" Y="4.2632564145606011E-14" />
                </DrawingGroup.Transform>
                <GeometryDrawing Brush="#FF00A1D6"
                                 Geometry="F1 M1024,1024z M0,0z M384,608L306.67,530.67Q286.67,512 259.34,512 232,512 212,531.34 192,550.67 192,576.67 192,602.67 210.67,626.67L333.33,749.33Q354.67,769.33 384,769.33 413.33,769.33 434.67,749.33L813.33,370.67Q832,350.67 832,323.34 832,296 812,276 792,256 764.66,256 737.33,256 717.33,274.67L384,608z M170.67,0L853.33,0Q925.33,1.33 974,50 1022.67,98.67 1024,170.67L1024,853.33Q1022.67,925.33 974,974 925.33,1022.67 853.33,1024L170.67,1024Q98.67,1022.67 50,974 1.32999999999996,925.33 -4.2632564145606E-14,853.33L-4.2632564145606E-14,170.67Q1.32999999999996,98.67 50,50 98.67,1.32999999999996 170.67,-4.2632564145606E-14z" />
            </DrawingGroup>
        </DrawingImage.Drawing>
    </DrawingImage>

    <ControlTheme x:Key="CheckBoxStyle" TargetType="{x:Type CheckBox}">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="BorderBrush" Value="{x:Null}" />

        <Setter Property="Template">
            <ControlTemplate TargetType="{x:Type CheckBox}">
                <Grid ColumnDefinitions="*,*">

                    <Border
                        Grid.Column="0"
                        Padding="0,0,5,0"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                        <Image
                            x:Name="nameIcon"
                            Width="14"
                            Height="14"
                            Margin="0,3"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center" />
                    </Border>

                    <ContentPresenter
                        Name="content"
                        Grid.Column="1"
                        Content="{TemplateBinding Content}"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center" />
                </Grid>
            </ControlTemplate>
        </Setter>
        <Style Selector="^ /template/ Border Image#nameIcon">
            <Setter Property="Source" Value="{StaticResource CheckBox.UncheckedDrawingImage}" />
        </Style>
        <Style Selector="^:checked /template/ Border Image#nameIcon">
            <Setter Property="Source" Value="{StaticResource CheckBox.CheckedDrawingImage}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>