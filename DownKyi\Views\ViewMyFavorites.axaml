﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="DownKyi.Views.ViewMyFavorites"
             xmlns:custom="clr-namespace:DownKyi.CustomControl"
             xmlns:vm="clr-namespace:DownKyi.ViewModels"
             xmlns:vmp="clr-namespace:DownKyi.ViewModels.PageViewModels"
             xmlns:asyncImageLoader="clr-namespace:DownKyi.CustomControl.AsyncImageLoader"
             x:DataType="vm:ViewMyFavoritesViewModel">
    <UserControl.Resources>
        <ControlTheme x:Key="MediaListStyle" TargetType="{x:Type ListBoxItem}" x:DataType="vmp:FavoritesMedia">
            <Setter Property="IsSelected" Value="{Binding IsSelected}" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ListBoxItem}">
                        <Grid
                            Name="NameMediaPanel"
                            Width="190"
                            Margin="15,15,10,5" RowDefinitions="115,*,*">
                            <Border
                                Name="NameCover"
                                Grid.Row="0"
                                Width="185"
                                Height="115"
                                HorizontalAlignment="Center"
                                CornerRadius="5">
                                <Interaction.Behaviors>
                                    <DataTriggerBehavior
                                        Binding="{Binding IsPointerOver,RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Value="True">
                                        <ChangePropertyAction PropertyName="IsVisible" TargetObject="NameInfoPanel"
                                                              Value="True" />
                                    </DataTriggerBehavior>
                                    <DataTriggerBehavior
                                        Binding="{Binding IsPointerOver,RelativeSource={RelativeSource Mode=TemplatedParent}}"
                                        Value="False">
                                        <ChangePropertyAction PropertyName="IsVisible" TargetObject="NameInfoPanel"
                                                              Value="False" />
                                    </DataTriggerBehavior>
                                </Interaction.Behaviors>
                                <Border.Background>
                                    <!--  长宽比：1.6  -->
                                    <ImageBrush asyncImageLoader:ImageBrushLoader.Source="{Binding Cover}"
                                                asyncImageLoader:ImageBrushLoader.Width="185"
                                                asyncImageLoader:ImageBrushLoader.Height="115" />
                                </Border.Background>
                                <Border
                                    Name="NameInfoPanel"
                                    Background="{DynamicResource BrushMask}"
                                    CornerRadius="5"
                                    IsVisible="False">
                                    <StackPanel Margin="20,0,0,0" VerticalAlignment="Center">
                                        <StackPanel Height="20" Orientation="Horizontal">
                                            <TextBlock
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushText}"
                                                Text="{DynamicResource FavoritesPlayNumber}" />
                                            <TextBlock
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushText}"
                                                Text="{Binding PlayNumber}" />
                                        </StackPanel>
                                        <StackPanel Height="20" Orientation="Horizontal">
                                            <TextBlock
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushText}"
                                                Text="{DynamicResource FavoritesFavoriteNumber}" />
                                            <TextBlock
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushText}"
                                                Text="{Binding FavoriteNumber}" />
                                        </StackPanel>
                                        <StackPanel Height="20" Orientation="Horizontal">
                                            <TextBlock
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushText}"
                                                Text="{DynamicResource FavoritesUpName}" />
                                            <TextBlock
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushText}"
                                                Text="{Binding UpName}" />
                                        </StackPanel>
                                        <StackPanel Height="20" Orientation="Horizontal">
                                            <TextBlock
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushText}"
                                                Text="{DynamicResource FavoritesCreateTime}" />
                                            <TextBlock
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushText}"
                                                Text="{Binding CreateTime}" />
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </Border>
                            <Image
                                Name="NameChecked"
                                Grid.Row="0"
                                Width="24"
                                Height="24"
                                Margin="0,10,10,0"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Top"
                                Source="/Resources/checked.png" />
                            <TextBlock
                                Name="NameTitle"
                                Grid.Row="1"
                                Width="185"
                                Height="35"
                                Margin="0,5"
                                Cursor="Hand"
                                Foreground="{DynamicResource BrushTextDark}"
                                Text="{Binding Title}"
                                TextTrimming="CharacterEllipsis"
                                TextWrapping="WrapWithOverflow"
                                ToolTip.Tip="{Binding Title}">
                                <Interaction.Behaviors>
                                    <EventTriggerBehavior EventName="PointerReleased">
                                        <InvokeCommandAction Command="{Binding TitleCommand}"
                                                             CommandParameter="{ReflectionBinding DataContext.PageName, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=UserControl}}" />
                                    </EventTriggerBehavior>
                                </Interaction.Behaviors>
                            </TextBlock>
                            <Grid Grid.Row="2" ColumnDefinitions="120,*">
                                <StackPanel Grid.Column="0" Orientation="Horizontal">
                                    <TextBlock
                                        FontSize="12"
                                        Foreground="{DynamicResource BrushTextGrey}"
                                        Text="{DynamicResource FavoritesFavTime}" />
                                    <TextBlock
                                        FontSize="12"
                                        Foreground="{DynamicResource BrushTextGrey}"
                                        Text="{Binding FavTime}" />
                                </StackPanel>
                            </Grid>
                        </Grid>
                        <!--<ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="nameChecked" Property="Visibility" Value="Visible" />
                            </Trigger>
                            <Trigger Property="IsSelected" Value="False">
                                <Setter TargetName="nameChecked" Property="Visibility" Value="Hidden" />
                            </Trigger>
                            <Trigger SourceName="nameCover" Property="IsMouseOver" Value="True">
                                <Setter TargetName="nameInfoPanel" Property="Visibility" Value="Visible" />
                            </Trigger>
                            <Trigger SourceName="nameTitle" Property="IsMouseOver" Value="True">
                                <Setter TargetName="nameTitle" Property="Foreground"
                                        Value="{DynamicResource BrushPrimary}" />
                            </Trigger>
                        </ControlTemplate.Triggers>-->
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style Selector="^ /template/ Image#NameChecked">
                <Setter Property="IsVisible" Value="False" />
            </Style>
            <Style Selector="^:selected /template/ Image#NameChecked">
                <Setter Property="IsVisible" Value="True" />
            </Style>
            <Style Selector="^ /template/ TextBlock#NameTitle:pointerover">
                <Setter Property="Foreground" Value="{DynamicResource BrushPrimary}" />
            </Style>
        </ControlTheme>
    </UserControl.Resources>
    <Grid RowDefinitions="50,1,*">
        <Grid Grid.Row="0" ColumnDefinitions="100,*,100">
            <Button
                Grid.Column="0"
                Margin="10,5,0,5"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Command="{Binding BackSpaceCommand}"
                Theme="{StaticResource ImageBtnStyle}">
                <StackPanel Orientation="Horizontal">
                    <ContentControl Width="24" Height="24">
                        <Path
                            Width="{Binding ArrowBack.Width}"
                            Height="{Binding ArrowBack.Height}"
                            Data="{Binding ArrowBack.Data}"
                            Fill="{Binding ArrowBack.Fill}"
                            Stretch="None" />
                    </ContentControl>
                    <TextBlock
                        VerticalAlignment="Center"
                        FontSize="16"
                        Foreground="{DynamicResource BrushTextDark}"
                        Text="{DynamicResource PublicFavorites}" />
                </StackPanel>
            </Button>

            <Button
                Grid.Column="2"
                Width="24"
                Height="24"
                Margin="10,5"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Command="{Binding DownloadManagerCommand}"
                Theme="{StaticResource ImageBtnStyle}"
                ToolTip.Tip="{DynamicResource DownloadManager}">
                <ContentControl>
                    <Path
                        Width="{Binding DownloadManage.Width}"
                        Height="{Binding DownloadManage.Height}"
                        Data="{Binding DownloadManage.Data}"
                        Fill="{Binding DownloadManage.Fill}"
                        Stretch="Uniform" />
                </ContentControl>
            </Button>
        </Grid>
        <TextBlock Grid.Row="1" Background="{DynamicResource BrushBorder}" />

        <Grid Grid.Row="2" IsVisible="{Binding ContentVisibility}" ColumnDefinitions="200,*">

            <!--  左侧tab header  -->
            <ListBox
                Name="NameLeftTabHeaders"
                Grid.Column="0"
                BorderThickness="0"
                IsEnabled="{Binding IsEnabled}"
                ItemsSource="{Binding TabHeaders}"
                ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                ScrollViewer.VerticalScrollBarVisibility="Hidden"
                ItemContainerTheme="{StaticResource LeftTabHeaderItemStyle}"
                Theme="{StaticResource LeftTabHeaderStyle}"
                SelectedIndex="{Binding SelectTabId, Mode=TwoWay}">
                <Interaction.Behaviors>
                    <EventTriggerBehavior EventName="SelectionChanged">
                        <InvokeCommandAction Command="{Binding LeftTabHeadersCommand}"
                                             CommandParameter="{Binding ElementName=NameLeftTabHeaders, Path=SelectedItem}" />
                    </EventTriggerBehavior>
                </Interaction.Behaviors>
            </ListBox>

            <Grid
                Name="NameMediaPanel"
                Grid.Column="1"
                IsVisible="{Binding MediaContentVisibility}"
                RowDefinitions="*,1,50">

                <ListBox
                    x:Name="NameMedias"
                    Grid.Row="0"
                    BorderThickness="0"
                    ItemsSource="{Binding Medias}"
                    ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                    SelectionMode="Multiple"
                    ItemContainerTheme="{StaticResource MediaListStyle}">
                    <Interaction.Behaviors>
                        <EventTriggerBehavior EventName="SelectionChanged">
                            <InvokeCommandAction Command="{Binding MediasCommand}"
                                                 CommandParameter="{Binding ElementName=NameMedias, Path=SelectedItems}" />
                        </EventTriggerBehavior>
                    </Interaction.Behaviors>

                    <ListBox.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel />
                        </ItemsPanelTemplate>
                    </ListBox.ItemsPanel>
                    <ListBox.Theme>
                        <ControlTheme TargetType="ListBox">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="ListBox">
                                        <Border
                                            x:Name="Bd"
                                            Padding="0"
                                            Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}">
                                            <ScrollViewer Focusable="False">
                                                <ItemsPresenter ItemsPanel="{TemplateBinding ItemsPanel}" />
                                            </ScrollViewer>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </ControlTheme>
                    </ListBox.Theme>
                </ListBox>

                <TextBlock Grid.Row="1" Background="{DynamicResource BrushBorder}" />

                <Grid Grid.Row="2" ColumnDefinitions="80,*,100,90">
                    <CheckBox
                        Grid.Column="0"
                        Margin="10,0,0,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Command="{Binding SelectAllCommand}"
                        Content="{DynamicResource SelectAll}"
                        Foreground="{DynamicResource BrushTextDark}"
                        IsChecked="{Binding IsSelectAll, Mode=TwoWay}" />

                    <custom:CustomPager
                        Grid.Column="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        DataContext="{Binding Pager}" />

                    <Button
                        Grid.Column="2"
                        Margin="0,0,10,0"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Center"
                        Command="{Binding AddToDownloadCommand}"
                        Content="{DynamicResource DownloadSelectedPublication}"
                        FontSize="12"
                        Foreground="{DynamicResource BrushText}"
                        Theme="{StaticResource BtnStyle}" />

                    <Button
                        Grid.Column="3"
                        Margin="0,0,10,0"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Center"
                        Command="{Binding AddAllToDownloadCommand}"
                        Content="{DynamicResource DownloadAllPublication}"
                        FontSize="12"
                        Foreground="{DynamicResource BrushText}"
                        Theme="{StaticResource BtnStyle}" />
                </Grid>
            </Grid>

            <!--  加载gif  -->
            <StackPanel
                Grid.Column="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Orientation="Vertical"
                IsVisible="{Binding MediaLoadingVisibility}">
                <custom:Loading
                    Width="40"
                    Height="40"
                    Foreground="Gray"
                    IsActive="{Binding MediaLoading}" />
            </StackPanel>

            <!--  没有数据提示  -->
            <Image
                Grid.Column="1"
                Width="256"
                Height="256"
                Source="/Resources/no-data.png"
                IsVisible="{Binding MediaNoDataVisibility}" />
        </Grid>
        <!--  加载gif  -->
        <StackPanel
            Grid.Row="2"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Orientation="Vertical"
            IsVisible="{Binding LoadingVisibility}">
            <custom:Loading
                Width="40"
                Height="40"
                Foreground="Gray"
                IsActive="{Binding Loading}" />
            <TextBlock
                Margin="0,10,0,0"
                FontSize="14"
                Foreground="{DynamicResource BrushTextDark}"
                Text="{DynamicResource PublicationWait}" />
        </StackPanel>

        <!--  没有数据提示  -->
        <Image
            Grid.Row="2"
            Width="256"
            Height="256"
            Source="/Resources/no-data.png"
            IsVisible="{Binding NoDataVisibility}" />
    </Grid>
</UserControl>