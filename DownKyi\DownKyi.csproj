﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net6.0</TargetFramework>
        <Nullable>enable</Nullable>
        <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
        <ApplicationManifest>app.manifest</ApplicationManifest>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
        <SatelliteResourceLanguages>zh-Hans</SatelliteResourceLanguages>
        <ApplicationIcon>Resources\favicon.ico</ApplicationIcon>
    </PropertyGroup>
    
    <ItemGroup>
        <AvaloniaResource Include="Resources\**"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia"/>
        <PackageReference Include="Avalonia.Controls.DataGrid"/>
        <PackageReference Include="Avalonia.Desktop"/>
        <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
        <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics"/>
        <PackageReference Include="Avalonia.Themes.Simple"/>
        <PackageReference Include="Downloader"/>
        <PackageReference Include="FreeSql" />
        <PackageReference Include="FreeSql.DbContext" />
        <PackageReference Include="FreeSql.Provider.SqliteCore" />
        <PackageReference Include="Prism.Avalonia"/>
        <PackageReference Include="Prism.DryIoc.Avalonia"/>
        <PackageReference Include="Xaml.Behaviors"/>
        <PackageReference Include="System.Formats.Nrbf" />
    </ItemGroup>


    <ItemGroup>
        <ProjectReference Include="..\DownKyi.Core\DownKyi.Core.csproj"/>
    </ItemGroup>
</Project>
