﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="DownKyi.Views.Toolbox.ViewExtractMedia"
             xmlns:vm="clr-namespace:DownKyi.ViewModels.Toolbox"
             x:DataType="vm:ViewExtractMediaViewModel">
    <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Disabled">
        <Grid Margin="50,0" RowDefinitions="200,*">
            <StackPanel Grid.Row="0" Orientation="Vertical">
                <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                    <TextBlock
                        FontSize="18"
                        Foreground="{DynamicResource BrushTextDark}"
                        Text="{DynamicResource ExtractMedia}" />
                </StackPanel>

                <StackPanel
                    Margin="0,20,0,0"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <TextBlock VerticalAlignment="Center" Text="{DynamicResource VideoFilePath}" />
                    <TextBox
                        Width="600"
                        Height="65"
                        Margin="0,10,10,10"
                        VerticalContentAlignment="Top"
                        IsReadOnly="True"
                        Text="{Binding VideoPathsStr, Mode=TwoWay}" />

                    <Button
                        Margin="30,0,0,0"
                        VerticalAlignment="Center"
                        Command="{Binding SelectVideoCommand}"
                        Content="{DynamicResource SelectVideo}"
                        Theme="{StaticResource BtnBorderStyle}" />
                </StackPanel>
                <Grid Margin="0,20,0,0" ColumnDefinitions="200,200">

                    <Button
                        Grid.Column="0"
                        Width="90"
                        Margin="30,0,0,0"
                        VerticalAlignment="Center"
                        Command="{Binding ExtractAudioCommand}"
                        Content="{DynamicResource ExtractAudio}"
                        Theme="{StaticResource BtnBorderStyle}" />
                    <Button
                        Grid.Column="1"
                        Width="90"
                        Margin="30,0,0,0"
                        VerticalAlignment="Center"
                        Command="{Binding ExtractVideoCommand}"
                        Content="{DynamicResource ExtractVideo}"
                        Theme="{StaticResource BtnBorderStyle}" />
                </Grid>
            </StackPanel>
            <Grid
                Grid.Row="1"
                Margin="0,20"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch" RowDefinitions="20,*">
                <TextBlock
                    Grid.Row="0"
                    VerticalAlignment="Bottom"
                    Text="{DynamicResource OutputInfo}" />
                <TextBox
                    Name="NameStatus"
                    Grid.Row="1"
                    Background="Black"
                    Foreground="White"
                    ScrollViewer.HorizontalScrollBarVisibility="Visible"
                    IsReadOnly="True"
                    Text="{Binding Status, Mode=TwoWay}"
                    ScrollViewer.VerticalScrollBarVisibility="Visible">
                    <Interaction.Behaviors>
                        <EventTriggerBehavior EventName="TextChanged">
                            <InvokeCommandAction
                                Command="{Binding StatusCommand}"
                                CommandParameter="{Binding $self}" />
                        </EventTriggerBehavior>
                    </Interaction.Behaviors>
                </TextBox>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>