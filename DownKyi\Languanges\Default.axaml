<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">
    <!--  App  -->
    <system:String x:Key="AppName">哔哩下载姬</system:String>
    <system:String x:Key="Unknown">未知</system:String>

    <!--  系统  -->
    <system:String x:Key="Close">关闭</system:String>
    <system:String x:Key="Maximize">最大化</system:String>
    <system:String x:Key="Minimize">最小化</system:String>
    <system:String x:Key="Restore">还原</system:String>
    <system:String x:Key="Skin">皮肤</system:String>

    <!--  Index  -->
    <system:String x:Key="Login">登录</system:String>
    <system:String x:Key="IndexHintText">请输入B站网址链接或av、BV号等……</system:String>
    <system:String x:Key="IndexHintTextSimple">请输入B站视频播放地址……</system:String>
    <system:String x:Key="Settings">设置</system:String>
    <system:String x:Key="DownloadManager">下载管理</system:String>
    <system:String x:Key="Toolbox">工具箱</system:String>

    <!--  Login  -->
    <system:String x:Key="ScanToLogin">扫描二维码登录</system:String>
    <system:String x:Key="ScanLoginTip" xml:space="preserve">请使用哔哩哔哩客户端&#x000A;扫码登录&#x000A;或扫码下载APP</system:String>
    <system:String x:Key="GetLoginUrlFailed">获取登录链接失败！</system:String>
    <system:String x:Key="LoginKeyError">密钥错误！尝试重新获取！</system:String>
    <system:String x:Key="LoginTimeOut">已超时！尝试重新获取二维码！</system:String>
    <system:String x:Key="LoginSuccessful">登录成功！(3秒后自动回到主页)</system:String>
    <system:String x:Key="LoginFailed">未保存登录信息！(3秒后自动回到主页)</system:String>

    <!--  MySpace  -->
    <system:String x:Key="MySpace">我的个人空间</system:String><system:String x:Key="Logout">退出登录</system:String>
    <system:String x:Key="MySpaceWait">请稍等，马上就好~</system:String>
    <system:String x:Key="Level">等级</system:String>
    <system:String x:Key="Following">关注数</system:String>
    <system:String x:Key="Whisper">悄悄关注数</system:String>
    <system:String x:Key="Follower">粉丝数</system:String>
    <system:String x:Key="Black">黑名单数</system:String>
    <system:String x:Key="Moral">节操值</system:String>
    <system:String x:Key="Silence">封禁状态</system:String>
    <system:String x:Key="Favorites">收藏夹</system:String>
    <system:String x:Key="Subscription">我的订阅</system:String>
    <system:String x:Key="ToView">稍后再看</system:String>
    <system:String x:Key="History">历史记录</system:String>
    <system:String x:Key="Normal">正常</system:String>
    <system:String x:Key="Ban">封停</system:String>
    
    <!--  UserSpace  -->
    <system:String x:Key="UserSpace">个人空间</system:String>
    <system:String x:Key="Followed">已关注</system:String>
    <system:String x:Key="NotFollowed">未关注</system:String>
    <system:String x:Key="FollowingCount">关注数</system:String>
    <system:String x:Key="FollowerCount">粉丝数</system:String>
    <system:String x:Key="LikesCount">获赞数</system:String>
    <system:String x:Key="ArchiveViewCount">播放数</system:String>
    <system:String x:Key="ArticleViewCount">阅读数</system:String>
    <system:String x:Key="AllPublicationZones">全部</system:String>
    <system:String x:Key="Publication">投稿视频</system:String>
    <system:String x:Key="Channel">频道</system:String>
    <system:String x:Key="SeasonsSeries">合集和列表</system:String>
    <system:String x:Key="UserSpaceWait">请稍等，马上就好~</system:String>
    
    <!--  PublicFavorites  -->
    <system:String x:Key="PublicFavorites">收藏夹</system:String>
    <system:String x:Key="FavoritesMediaCount">个内容</system:String>
    
    <!--  MyFavorites  -->
    <system:String x:Key="FavoritesPlayNumber">播放：</system:String>
    <system:String x:Key="FavoritesFavoriteNumber">收藏：</system:String>
    <system:String x:Key="FavoritesUpName">UP主：</system:String>
    <system:String x:Key="FavoritesCreateTime">投稿：</system:String>
    <system:String x:Key="FavoritesFavTime">收藏于：</system:String>
    
    <!--  MyBangumiFollow  -->
    <system:String x:Key="MyBangumiFollow">我的订阅</system:String>
    <system:String x:Key="FollowAnime">追番</system:String>
    <system:String x:Key="FollowMovie">追剧</system:String>
    <system:String x:Key="BangumiNotWatched">尚未观看</system:String>
    
    <!--  MyHistory  -->
    <system:String x:Key="MyHistory">历史记录</system:String>
    <system:String x:Key="HistoryFinished">已看完</system:String>
    <system:String x:Key="HistoryStarted">刚开始</system:String>
    <system:String x:Key="HistoryWatch">看到</system:String>
    
    <!--  MyToView  -->
    <system:String x:Key="MyToView">稍后再看</system:String>
    
    <!--  Publication  -->
    <system:String x:Key="DownloadSelectedPublication">下载选中项</system:String>
    <system:String x:Key="DownloadAllPublication">下载全部</system:String>
    <system:String x:Key="PublicationWait">请稍等，马上就好~</system:String>
    
    <!--  Friend  -->
    <system:String x:Key="Friend">好友</system:String>
    <system:String x:Key="FriendFollowing">关注</system:String>
    <system:String x:Key="FriendFollower">粉丝</system:String>
    <system:String x:Key="FollowingWait">请稍等，马上就好~</system:String>
    <system:String x:Key="FollowerWait">请稍等，马上就好~</system:String>
    <system:String x:Key="AllFollowing">全部关注</system:String>
    <system:String x:Key="WhisperFollowing">悄悄关注</system:String>
    
    <!--  VideoDetail  -->
    <system:String x:Key="CopyCover">复制封面图片</system:String>
    <system:String x:Key="CopyCoverUrl">复制封面URL</system:String>
    <system:String x:Key="Play">播放</system:String>
    <system:String x:Key="Danmaku">弹幕</system:String>
    <system:String x:Key="Like">点赞</system:String>
    <system:String x:Key="Coin">投币</system:String>
    <system:String x:Key="Favorite">收藏</system:String>
    <system:String x:Key="Share">分享</system:String>
    <system:String x:Key="Reply">评论</system:String>
    <system:String x:Key="Anime">番剧</system:String>
    <system:String x:Key="Movie">电影</system:String>
    <system:String x:Key="Documentary">纪录片</system:String>
    <system:String x:Key="Guochuang">国创</system:String>
    <system:String x:Key="TV">电视剧</system:String>
    <system:String x:Key="Entertainment">综艺</system:String>
    <system:String x:Key="Cheese">课程</system:String>
    <system:String x:Key="Order">序号</system:String>
    <system:String x:Key="Name">名称</system:String>
    <system:String x:Key="Duration">时长</system:String>
    <system:String x:Key="AudioQuality">音质</system:String>
    <system:String x:Key="VideoQuality">画质</system:String>
    <system:String x:Key="VideoCodec">视频编码</system:String>
    <system:String x:Key="SelectAll">全选</system:String>
    <system:String x:Key="Search">搜索</system:String>
    <system:String x:Key="SearchVideoByName">搜索视频名称</system:String>
    <system:String x:Key="Parse">解析</system:String>
    <system:String x:Key="ParseVideo">解析视频</system:String>
    <system:String x:Key="DownloadSelected">下载选中项</system:String>
    <system:String x:Key="AddAllToDownload">下载全部</system:String>
    <system:String x:Key="TipAlreadyToAddDownloading">已经添加到下载列表~</system:String>
    <system:String x:Key="TipAlreadyToAddDownloaded">已经下载完成~</system:String>
    <system:String x:Key="TipAddDownloadingZero">没有选中项符合下载要求！</system:String>
    <system:String x:Key="TipAddDownloadingFinished1">成功添加了</system:String>
    <system:String x:Key="TipAddDownloadingFinished2">项~</system:String>
    
    <!--  DownloadManager  -->
    <system:String x:Key="Downloading">正在下载</system:String>
    <system:String x:Key="DownloadFinished">已下载</system:String>
    <system:String x:Key="DownloadSuccess">下载完成</system:String>
    <system:String x:Key="DownloadingAudio">音频</system:String>
    <system:String x:Key="DownloadingVideo">视频</system:String>
    <system:String x:Key="DownloadingDanmaku">弹幕</system:String>
    <system:String x:Key="DownloadingSubtitle">字幕</system:String>
    <system:String x:Key="DownloadingCover">封面</system:String>
    <system:String x:Key="Parsing">正在解析……</system:String>
    <system:String x:Key="WhileDownloading">下载中……</system:String>
    <system:String x:Key="MixedFlow">混流中……</system:String>
    <system:String x:Key="ConcatVideos">视频合并中……</system:String>
    <system:String x:Key="Pausing">暂停中……</system:String>
    <system:String x:Key="Waiting">等待中……</system:String>
    <system:String x:Key="DownloadFailed">下载失败</system:String>
    <system:String x:Key="TotalDownloading1">正在下载</system:String>
    <system:String x:Key="TotalDownloading2">个视频！</system:String>
    <system:String x:Key="PauseAllDownloading">全部暂停</system:String>
    <system:String x:Key="ContinueAllDownloading">全部开始</system:String>
    <system:String x:Key="DeleteAllDownloading">全部删除</system:String>
    <system:String x:Key="TotalDownloaded1">已下载</system:String>
    <system:String x:Key="TotalDownloaded2">个视频！</system:String>
    <system:String x:Key="DownloadedSortByTimeAsc">按下载时间升序</system:String>
    <system:String x:Key="DownloadedSortByTimeDesc">按下载时间降序</system:String>
    <system:String x:Key="DownloadedSortByOrder">按序号排序</system:String>
    <system:String x:Key="ClearAllDownloaded">清空所有记录</system:String>
    <system:String x:Key="StartDownload">开始</system:String>
    <system:String x:Key="PauseDownload">暂停</system:String>
    <system:String x:Key="RetryDownload">重试</system:String>
    <system:String x:Key="DeleteDownload">移除</system:String>
    <system:String x:Key="OpenFolder">打开文件夹</system:String>
    <system:String x:Key="OpenVideo">播放</system:String>
    
    <!--  Settings  -->
    <system:String x:Key="PressEnterToApplySettingTip">按回车键应用设置</system:String>
    <system:String x:Key="Basic">基本</system:String>
    <system:String x:Key="Theme">主题</system:String>
    <system:String x:Key="ThemeLight">亮色</system:String>
    <system:String x:Key="ThemeDark">暗色</system:String>
    <system:String x:Key="ThemeAuto">自动</system:String>
    <system:String x:Key="AfterDownloadOperation">下载完成后的动作：</system:String>
    <system:String x:Key="AfterDownloadOperationNone">无</system:String>
    <system:String x:Key="AfterDownloadOperationCloseApp">关闭程序</system:String>
    <system:String x:Key="AfterDownloadOperationCloseSystem">关闭计算机</system:String>
    <system:String x:Key="ListenClipboard">监听剪贴板</system:String>
    <system:String x:Key="VideoAutoParse">视频自动解析</system:String>
    <system:String x:Key="VideoParseScope">视频解析范围：</system:String>
    <system:String x:Key="RepeatDownloadStrategy">重复下载策略：</system:String>
    <system:String x:Key="RepeatDownloadAsk">每次询问</system:String>
    <system:String x:Key="RepeatDownloadReDownload">重新下载</system:String>
    <system:String x:Key="RepeatDownloadReJumpOver">跳过重复项</system:String>
    <system:String x:Key="RepeatFileAutoAddNumberSuffix">重复文件自动添加数字序号后缀</system:String>
    <system:String x:Key="AutoDownloadAll">解析后自动下载已解析视频</system:String>
    <system:String x:Key="Network">网络</system:String>
    <system:String x:Key="UseSSL">启用https（若下载器提示SSL错误，则关闭此项）</system:String>
    <system:String x:Key="UserAgent">UserAgent：</system:String>
    <system:String x:Key="NetworkProxySetting">网络代理设置</system:String>
    <system:String x:Key="NetworkProxyNone">无</system:String>
    <system:String x:Key="NetworkProxySystem">使用系统代理</system:String>
    <system:String x:Key="NetworkProxyCustom">使用自定义代理</system:String>
    <system:String x:Key="SelectDownloader">选择下载器（重启生效）：</system:String>
    <system:String x:Key="BuiltinDownloader">内建下载器</system:String>
    <system:String x:Key="Aria2cDownloader">Aria2下载器</system:String>
    <system:String x:Key="CustomAria2cDownloader">自定义Aria2下载器</system:String>
    <system:String x:Key="AriaServerHost">Aria服务器地址：</system:String>
    <system:String x:Key="AriaServerPort">Aria服务器端口：</system:String>
    <system:String x:Key="AriaServerToken">Aria服务器Token：</system:String>
    <system:String x:Key="AriaLogLevel">Aria日志等级：</system:String>
    <system:String x:Key="AriaMaxConcurrentDownloads">Aria同时下载数：</system:String>
    <system:String x:Key="AriaSplit">Aria最大线程数：</system:String>
    <system:String x:Key="AriaDownloadLimit">Aria下载速度限制(KB/s)</system:String>
    <system:String x:Key="AriaMaxOverallDownloadLimit">全局下载速度限制[0: 无限制]</system:String>
    <system:String x:Key="AriaMaxDownloadLimit">单任务下载速度限制[0: 无限制]</system:String>
    <system:String x:Key="AriaFileAllocation">Aria文件预分配：</system:String>
    <system:String x:Key="IsHttpProxy">使用Http代理</system:String>
    <system:String x:Key="HttpProxy">代理地址：</system:String>
    <system:String x:Key="HttpProxyPort">端口：</system:String>
    <system:String x:Key="MaxCurrentDownloads">同时下载数：</system:String>
    <system:String x:Key="Split">最大线程数：</system:String>
    <system:String x:Key="Video">视频</system:String>
    <system:String x:Key="FirstVideoCodecs">优先下载的视频编码：</system:String>
    <system:String x:Key="FirstVideoQuality">优先下载的视频画质：</system:String>
    <system:String x:Key="FirstAudioQuality">优先下载的视频音质：</system:String>
    <system:String x:Key="VideoParseType">首选视频解析方式：</system:String>
    <system:String x:Key="IsTranscodingFlvToMp4">下载FLV视频后转码为mp4</system:String>
    <system:String x:Key="IsTranscodingAacToMp3">下载AAC音频后转码为mp3</system:String>
    <system:String x:Key="IsUseDefaultDirectory">使用默认下载目录</system:String>
    <system:String x:Key="GenerateVideoMetadata">生成视频元数据</system:String>
    <system:String x:Key="DefaultDirectory">默认下载目录：</system:String>
    <system:String x:Key="DefaultDirectoryTip">默认将文件下载到该文件夹中</system:String>
    <system:String x:Key="ChangeDirectory">更改目录</system:String>
    <system:String x:Key="FileNameParts">文件命名格式</system:String>
    <system:String x:Key="FileName">文件名：</system:String>
    <system:String x:Key="OptionalFields">可选字段：</system:String>
    <system:String x:Key="DisplayOrder">序号</system:String>
    <system:String x:Key="DisplaySection">视频章节</system:String>
    <system:String x:Key="DisplayMainTitle">视频标题</system:String>
    <system:String x:Key="DisplayPageTitle">分P标题</system:String>
    <system:String x:Key="DisplayVideoZone">视频分区</system:String>
    <system:String x:Key="DisplayAudioQuality">音质</system:String>
    <system:String x:Key="DisplayVideoQuality">画质</system:String>
    <system:String x:Key="DisplayVideoCodec">视频编码</system:String>
    <system:String x:Key="DisplayVideoPublishTime">视频发布时间</system:String>
    <system:String x:Key="DisplaySpace">空格</system:String>
    <system:String x:Key="DisplayUpMid">UP主ID</system:String>
    <system:String x:Key="DisplayUpName">UP主昵称</system:String>
    <system:String x:Key="Reset">恢复默认</system:String>
    <system:String x:Key="FileNameTimeFormat">时间格式：</system:String>
    <system:String x:Key="OrderFormat">序号格式：</system:String>
    <system:String x:Key="OrderFormatNatural">自然数</system:String>
    <system:String x:Key="OrderFormatLeadingZeros">前导零填充</system:String>
    <system:String x:Key="SettingDanmaku">弹幕</system:String>
    <system:String x:Key="FilterType">按类型屏蔽</system:String>
    <system:String x:Key="TopFilter">顶部</system:String>
    <system:String x:Key="BottomFilter">底部</system:String>
    <system:String x:Key="ScrollFilter">滚动</system:String>
    <system:String x:Key="Resolution">分辨率：</system:String>
    <system:String x:Key="FontName">字体名称：</system:String>
    <system:String x:Key="FontSize">字体大小：</system:String>
    <system:String x:Key="LineCount">限制行数：</system:String>
    <system:String x:Key="LayoutAlgorithm">布局算法：</system:String>
    <system:String x:Key="LayoutAlgorithmSync">速度同步</system:String>
    <system:String x:Key="LayoutAlgorithmSyncTip">每一条滚动弹幕的移动速度都是相同的</system:String>
    <system:String x:Key="LayoutAlgorithmAsync">速度异步</system:String>
    <system:String x:Key="LayoutAlgorithmAsyncTip">弹幕滚动速度取决于它的文本长度</system:String>
    <system:String x:Key="About">关于</system:String>
    <system:String x:Key="GotoHomepage">访问主页</system:String>
    <system:String x:Key="CurrentAppVersion">当前版本：</system:String>
    <system:String x:Key="CheckUpdate">检查更新</system:String>
    <system:String x:Key="Feedback">意见反馈</system:String>
    <system:String x:Key="ReceiveBetaVersion">接收测试版更新</system:String>
    <system:String x:Key="AutoUpdateWhenLaunch">启动时检查更新</system:String>
    <system:String x:Key="TipSettingUpdated">设置已更新~</system:String>
    <system:String x:Key="TipSettingFailed">设置失败！</system:String>
    <system:String x:Key="ThirdParty">第三方</system:String>
    <system:String x:Key="ThirdPartyName">名称</system:String>
    <system:String x:Key="ThirdPartyAuthor">作者</system:String>
    <system:String x:Key="ThirdPartyVersion">版本</system:String>
    <system:String x:Key="ThirdPartyLicense">许可证</system:String>
    <system:String x:Key="Disclaimer">免责申明</system:String>
    <system:String x:Key="Disclaimer1">1. 本软件只提供视频解析，不提供任何资源上传、存储到服务器的功能。</system:String>
    <system:String x:Key="Disclaimer2">2. 本软件仅解析来自B站的内容，不会对解析到的音视频进行二次编码，部分视频会进行有限的格式转换、拼接等操作。</system:String>
    <system:String x:Key="Disclaimer3">3. 本软件解析得到的所有内容均来自B站UP主上传、分享，其版权均归原作者所有。内容提供者、上传者（UP主）应对其提供、上传的内容承担全部责任。</system:String>
    <system:String x:Key="Disclaimer4">4. 本软件提供的所有资源，仅可用作学习交流使用，未经原作者授权，禁止用于其他用途。请在下载24小时内删除。为尊重作者版权，请前往资源的原始发布网站观看，支持原创，谢谢。</system:String>
    <system:String x:Key="Disclaimer5">5. 任何涉及商业盈利目的均不得使用，否则产生的一切后果将由您自己承担。</system:String>
    <system:String x:Key="Disclaimer6">6. 因使用本软件产生的版权问题，软件作者概不负责。</system:String>
    
    <!--  Toolbox  -->
    <system:String x:Key="BiliHelper">B站助手</system:String>
    <system:String x:Key="AvidAndBvidConversion">AV和BV互转</system:String>
    <system:String x:Key="AvidAndBvidConversionTip">AV号形如&quot;av123&quot;，BV号形如&quot;BVxxx&quot;。</system:String>
    <system:String x:Key="Avid">AVID:</system:String>
    <system:String x:Key="Bvid">BVID:</system:String>
    <system:String x:Key="GotoWeb">访问网页</system:String>
    <system:String x:Key="DanmakuUserID">弹幕中的userID:</system:String>
    <system:String x:Key="FindDanmakuSender">查询弹幕发送者-&gt;</system:String>
    <system:String x:Key="Delogo">去水印</system:String>
    <system:String x:Key="VideoFilePath">视频地址：</system:String>
    <system:String x:Key="SelectVideo">选择视频</system:String>
    <system:String x:Key="LogoSizeAndLocation">水印尺寸与位置（左上角为原点，单位：像素）</system:String>
    <system:String x:Key="LogoWidth">宽：</system:String>
    <system:String x:Key="LogoHeight">高：</system:String>
    <system:String x:Key="LogoX">X：</system:String>
    <system:String x:Key="LogoY">Y：</system:String>
    <system:String x:Key="TipWaitTaskFinished">请等待当前任务完成！</system:String>
    <system:String x:Key="TipNoSeletedVideo">没有选择视频！</system:String>
    <system:String x:Key="TipInputRightLogoWidth">请输入正确的宽度（正整数）！</system:String>
    <system:String x:Key="TipInputRightLogoHeight">请输入正确的高度（正整数）！</system:String>
    <system:String x:Key="TipInputRightLogoX">请输入正确的X（正整数）！</system:String>
    <system:String x:Key="TipInputRightLogoY">请输入正确的Y（正整数）！</system:String>
    <system:String x:Key="OutputInfo">输出信息：</system:String>
    <system:String x:Key="ExtractMedia">音视频分离</system:String>
    <system:String x:Key="ExtractAudio">提取音频</system:String>
    <system:String x:Key="ExtractVideo">提取视频</system:String>
    
    <!--  Dialog  -->
    <system:String x:Key="Info">信息</system:String>
    <system:String x:Key="Warning">警告</system:String>
    <system:String x:Key="Error">错误</system:String>
    <system:String x:Key="Allow">确定</system:String>
    <system:String x:Key="ActionUpdate">前往下载</system:String>
    <system:String x:Key="SkipCurrentVersion">跳过此版本</system:String>
    <system:String x:Key="NewVersionTitle">更新提示</system:String>
    <system:String x:Key="Cancel">取消</system:String>
    <system:String x:Key="ConfirmReboot">此项需重启生效，您确定要重新启动吗？</system:String>
    <system:String x:Key="ConfirmDelete">您确定要删除吗？</system:String>
    <system:String x:Key="SelectDirectory">请选择文件夹</system:String>
    <system:String x:Key="DirectoryError">路径错误！</system:String>
    
    
    <!--  ViewDownloadSetter  -->
    <system:String x:Key="DownloadSetter">下载设置</system:String>
    <system:String x:Key="Location">位置：</system:String>
    <system:String x:Key="Browse">浏览</system:String>
    <system:String x:Key="HardDiskFreeSpace">剩余空间：</system:String>
    <system:String x:Key="DownloadContent">下载内容：</system:String>
    <system:String x:Key="DownloadContent2">下载内容</system:String>
    <system:String x:Key="DownloadAll">所有</system:String>
    <system:String x:Key="DownloadAudio">音频</system:String>
    <system:String x:Key="DownloadVideo">视频</system:String>
    <system:String x:Key="DownloadDanmaku">弹幕</system:String>
    <system:String x:Key="DownloadSubtitle">字幕</system:String>
    <system:String x:Key="DownloadCover">封面</system:String>
    <system:String x:Key="IsDefaultDownloadDirectory">设此文件夹为默认下载文件夹</system:String>
    <system:String x:Key="IsDefaultDownloadDirectoryTip">选中后下次将不会弹出此窗口</system:String>
    <system:String x:Key="Download">下载</system:String>
    <system:String x:Key="WarningNullDirectory">文件夹路径不能为空</system:String>
    <system:String x:Key="DriveNotFound">该磁盘不存在</system:String>
    
    <!--  ViewParsingSelector  -->
    <system:String x:Key="ParsingSelector">选择解析项</system:String>
    <system:String x:Key="ParseNone">无</system:String>
    <system:String x:Key="ParseSelectedItem">解析选中项</system:String>
    <system:String x:Key="ParseCurrentSection">解析当前页视频</system:String>
    <system:String x:Key="ParseAll">解析所有视频</system:String>
    <system:String x:Key="SetParseDefault">设为默认，下次不再弹出</system:String>
    
    <!--  ViewAlreadyDownloaded  -->
    <system:String x:Key="Yes">是</system:String>
    <system:String x:Key="No">否</system:String>
    <system:String x:Key="ApplyToAll">全部应用</system:String>
    
    
    <!-- CustomControl -->
    <system:String x:Key="Page">页</system:String>
    <system:String x:Key="JumpTo">跳至</system:String>
</ResourceDictionary>