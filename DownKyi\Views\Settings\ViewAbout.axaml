<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="DownKyi.Views.Settings.ViewAbout"
             xmlns:prism="http://prismlibrary.com/"
             prism:ViewModelLocator.AutoWireViewModel="True"
             xmlns:vms="clr-namespace:DownKyi.ViewModels.Settings"
             x:DataType="vms:ViewAboutViewModel">
    <UserControl.Styles>
        <Style Selector="Button.link">
            <Setter Property="Foreground" Value="Blue" />
            <Setter Property="Padding" Value="0" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="Template">
                <ControlTemplate>
                    <ContentPresenter Content="{TemplateBinding Content}">
                        <ContentPresenter.Styles>
                            <Style Selector="TextBlock">
                                <Setter Property="Foreground" Value="{TemplateBinding Foreground}" />
                                <Setter Property="FontSize" Value="{TemplateBinding FontSize}" />
                                <Setter Property="TextDecorations" Value="Underline" />
                            </Style>
                        </ContentPresenter.Styles>
                    </ContentPresenter>
                </ControlTemplate>
            </Setter>
        </Style>
    </UserControl.Styles>

    <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="50,0" Orientation="Vertical">
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    FontSize="18"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource About}" />
                <TextBlock
                    Cursor="Hand"
                    FontSize="18"
                    Foreground="{DynamicResource BrushPrimary}"
                    Text="{Binding AppName}">
                    <Interaction.Behaviors>
                        <EventTriggerBehavior EventName="PointerReleased">
                            <InvokeCommandAction Command="{Binding AppNameCommand}" />
                        </EventTriggerBehavior>
                    </Interaction.Behaviors>
                </TextBlock>
            </StackPanel>
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource CurrentAppVersion}" />
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{Binding AppVersion}" />
                <Button
                    Width="75"
                    Margin="30,0,0,0"
                    Command="{Binding CheckUpdateCommand}"
                    Content="{DynamicResource CheckUpdate}"
                    FontSize="12"
                    Theme="{StaticResource BtnBorderStyle}" />
                <Button
                    Width="75"
                    Margin="20,0,0,0"
                    Command="{Binding FeedbackCommand}"
                    Content="{DynamicResource Feedback}"
                    FontSize="12"
                    Theme="{StaticResource BtnBorderStyle}" />
            </StackPanel>
            <CheckBox
                Grid.Column="0"
                Margin="0,20,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Command="{Binding ReceiveBetaVersionCommand}"
                Content="{DynamicResource ReceiveBetaVersion}"
                Foreground="{DynamicResource BrushTextDark}"
                IsChecked="{Binding IsReceiveBetaVersion, Mode=TwoWay}" />
            <CheckBox
                Margin="0,20,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Command="{Binding AutoUpdateWhenLaunchCommand}"
                Content="{DynamicResource AutoUpdateWhenLaunch}"
                Foreground="{DynamicResource BrushTextDark}"
                IsChecked="{Binding AutoUpdateWhenLaunch, Mode=TwoWay}" />
            <!--<StackPanel Margin="0,20,0,0" Orientation="Vertical">
                <TextBlock
                    Margin="0,0,0,10"
                    FontSize="14"
                    FontWeight="Bold"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource ThirdParty}" />

                <Border Padding="5"
                        HorizontalAlignment="Left"
                        BorderBrush="{DynamicResource BrushBorder}"
                        BorderThickness="1"
                        CornerRadius="5">
                    <Grid RowDefinitions="30,20,20,20,20,20,20,20,20" ColumnDefinitions="200,200,100,100">
                        <Grid.Styles>
                            <Style Selector="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center" />
                            </Style>
                        </Grid.Styles>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="0"
                            FontSize="12"
                            FontWeight="Bold"
                            Foreground="{DynamicResource BrushTextDark}"
                            Text="{DynamicResource ThirdPartyName}" />
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            FontSize="12"
                            FontWeight="Bold"
                            Foreground="{DynamicResource BrushTextDark}"
                            Text="{DynamicResource ThirdPartyAuthor}" />
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="2"
                            FontSize="12"
                            FontWeight="Bold"
                            Foreground="{DynamicResource BrushTextDark}"
                            Text="{DynamicResource ThirdPartyVersion}" />
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="3"
                            FontSize="12"
                            FontWeight="Bold"
                            Foreground="{DynamicResource BrushTextDark}"
                            Text="{DynamicResource ThirdPartyLicense}" />

                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="0"
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextDark}"
                            Text="Google.Protobuf" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextDark}"
                            Text="Google Inc." />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="2"
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextDark}"
                            Text="3.21.12" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="3"
                            FontSize="12">
                            <Button Classes="link" Command="{Binding ProtobufLicenseCommand}">LICENSE</Button>
                            ~1~ <Hyperlink Command="{Binding ProtobufLicenseCommand}"> @1@
                            ~1~     <TextBlock Text="LICENSE" /> @1@
                            ~1~ </Hyperlink> @1@
                        </TextBlock>

                        <TextBlock
                            Grid.Row="2"
                            Grid.Column="0"
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextDark}"
                            Text="Newtonsoft.Json" />
                        <TextBlock
                            Grid.Row="2"
                            Grid.Column="1"
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextDark}"
                            Text="James Newton-King" />
                        <TextBlock
                            Grid.Row="2"
                            Grid.Column="2"
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextDark}"
                            Text="13.0.2" />
                        <TextBlock
                            Grid.Row="2"
                            Grid.Column="3"
                            FontSize="12">
                            <Button
                                Command="{Binding NewtonsoftLicenseCommand}"
                                Classes="link">
                                MIT
                            </Button>
                        </TextBlock>


                    </Grid>
                </Border>
            </StackPanel>-->
            <!--  免责申明  -->
            <StackPanel Margin="0,20,0,0" Orientation="Vertical">
                <TextBlock
                    Margin="0,0,0,10"
                    FontSize="14"
                    FontWeight="Bold"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource Disclaimer}" />
                <TextBlock
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource Disclaimer1}"
                    TextWrapping="WrapWithOverflow" />
                <TextBlock
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource Disclaimer2}"
                    TextWrapping="WrapWithOverflow" />
                <TextBlock
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource Disclaimer3}"
                    TextWrapping="WrapWithOverflow" />
                <TextBlock
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource Disclaimer4}"
                    TextWrapping="WrapWithOverflow" />
                <TextBlock
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource Disclaimer5}"
                    TextWrapping="WrapWithOverflow" />
                <TextBlock
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource Disclaimer6}"
                    TextWrapping="WrapWithOverflow" />
            </StackPanel>
            <StackPanel Margin="10" />

        </StackPanel>
    </ScrollViewer>
</UserControl>