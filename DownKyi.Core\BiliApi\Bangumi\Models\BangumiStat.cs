﻿using DownKyi.Core.BiliApi.Models;
using Newtonsoft.Json;

namespace DownKyi.Core.BiliApi.Bangumi.Models;

public class BangumiStat : BaseModel
{
    [JsonProperty("coins")] public long Coins { get; set; }
    [<PERSON>son<PERSON>roperty("danmakus")] public long Danmakus { get; set; }
    [<PERSON><PERSON><PERSON><PERSON><PERSON>("favorite")] public long Favorite { get; set; }
    [<PERSON><PERSON><PERSON>roper<PERSON>("favorites")] public long Favorites { get; set; }
    [JsonProperty("likes")] public long Likes { get; set; }
    [Json<PERSON>roperty("reply")] public long Reply { get; set; }
    [JsonProperty("share")] public long Share { get; set; }
    [Json<PERSON>roperty("views")] public long Views { get; set; }
}