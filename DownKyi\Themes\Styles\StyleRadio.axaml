﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <DrawingImage x:Key="Radio.UnselectedDrawingImage">
        <DrawingImage.Drawing>
            <DrawingGroup ClipGeometry="M0,0 V1024 H1024 V0 H0 Z">
                <GeometryDrawing Brush="#FF000000"
                                 Geometry="F1 M1024,1024z M0,0z M512,0Q295,6 150.5,150.5 6,295 0,512 6,729 150.5,873.5 295,1018 512,1024 729,1018 873.5,873.5 1018,729 1024,512 1018,295 873.5,150.5 729,6 512,0z M512,960Q320,955 194.5,829.5 69,704 64,512 69,320 194.5,194.5 320,69 512,64 704,69 829.5,194.5 955,320 960,512 955,704 829.5,829.5 704,955 512,960z" />
            </DrawingGroup>
        </DrawingImage.Drawing>
    </DrawingImage>

    <DrawingImage x:Key="Radio.SelectedDrawingImage">
        <DrawingImage.Drawing>
            <DrawingGroup ClipGeometry="M0,0 V1024 H1024 V0 H0 Z">
                <GeometryDrawing Brush="#FF00A1D6"
                                 Geometry="F1 M1024,1024z M0,0z M512,0Q295,6 150.5,150.5 6,295 0,512 6,729 150.5,873.5 295,1018 512,1024 729,1018 873.5,873.5 1018,729 1024,512 1018,295 873.5,150.5 729,6 512,0z M512,960Q320,955 194.5,829.5 69,704 64,512 69,320 194.5,194.5 320,69 512,64 704,69 829.5,194.5 955,320 960,512 955,704 829.5,829.5 704,955 512,960z M256,512Q259,621 331,693 403,765 512,768 621,765 693,693 765,621 768,512 765,403 693,331 621,259 512,256 403,259 331,331 259,403 256,512z" />
            </DrawingGroup>
        </DrawingImage.Drawing>
    </DrawingImage>

    <ControlTheme x:Key="RadioStyle" TargetType="{x:Type RadioButton}">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="BorderBrush" Value="{x:Null}" />

        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RadioButton}">
                    <Grid ColumnDefinitions="*,*">
                        <Border
                            Grid.Column="0"
                            Padding="0,0,5,0"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <Image
                                x:Name="nameIcon"
                                Width="14"
                                Height="14"
                                Margin="0,3"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center" />
                        </Border>

                        <ContentPresenter
                            x:Name="content"
                            Grid.Column="1"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding Content}" />
                    </Grid>

                    <!--<ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="nameIcon" Property="Source"
                                    Value="{StaticResource Radio.SelectedDrawingImage}" />
                        </Trigger>
                        <Trigger Property="IsChecked" Value="False">
                            <Setter TargetName="nameIcon" Property="Source"
                                    Value="{StaticResource Radio.UnselectedDrawingImage}" />
                        </Trigger>
                    </ControlTemplate.Triggers>-->
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style Selector="^ /template/ Border Image#nameIcon">
            <Setter Property="Source" Value="{StaticResource Radio.UnselectedDrawingImage}" />
        </Style>
        <Style Selector="^:checked /template/ Border Image#nameIcon">
            <Setter Property="Source" Value="{StaticResource Radio.SelectedDrawingImage}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>