﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="DownKyi.Views.ViewPublicFavorites"
             xmlns:prism="http://prismlibrary.com/"
             prism:ViewModelLocator.AutoWireViewModel="True"
             xmlns:vm="clr-namespace:DownKyi.ViewModels"
             xmlns:vmp="clr-namespace:DownKyi.ViewModels.PageViewModels"
             xmlns:custom="clr-namespace:DownKyi.CustomControl"
             xmlns:asyncImageLoader="clr-namespace:DownKyi.CustomControl.AsyncImageLoader"
             x:DataType="vm:ViewPublicFavoritesViewModel">
    <Grid RowDefinitions="50,1,*">
        <Grid Grid.Row="0" ColumnDefinitions="100,*,100">
            <Button
                Grid.Column="0"
                Margin="10,5,0,5"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Command="{Binding BackSpaceCommand}"
                Theme="{StaticResource ImageBtnStyle}">
                <StackPanel Orientation="Horizontal">
                    <ContentControl Width="24" Height="24">
                        <Path
                            Width="{Binding ArrowBack.Width}"
                            Height="{Binding ArrowBack.Height}"
                            Data="{Binding ArrowBack.Data}"
                            Fill="{Binding ArrowBack.Fill}"
                            Stretch="None" />
                    </ContentControl>
                    <TextBlock
                        VerticalAlignment="Center"
                        FontSize="16"
                        Foreground="{DynamicResource BrushTextDark}"
                        Text="{DynamicResource PublicFavorites}" />
                </StackPanel>
            </Button>

            <Button
                Grid.Column="2"
                Width="24"
                Height="24"
                Margin="10,5"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Command="{Binding DownloadManagerCommand}"
                Theme="{StaticResource ImageBtnStyle}"
                ToolTip.Tip="{DynamicResource DownloadManager}">
                <ContentControl>
                    <Path
                        Width="{Binding DownloadManage.Width}"
                        Height="{Binding DownloadManage.Height}"
                        Data="{Binding DownloadManage.Data}"
                        Fill="{Binding DownloadManage.Fill}"
                        Stretch="Uniform" />
                </ContentControl>
            </Button>
        </Grid>

        <TextBlock Grid.Row="1" Background="{DynamicResource BrushBorder}" />

        <Grid Grid.Row="2" IsVisible="{Binding ContentVisibility}" ColumnDefinitions="320,*">
            <!--  左侧  -->
            <StackPanel
                Grid.Column="0"
                Margin="10,10,10,10"
                Orientation="Vertical">
                <Image asyncImageLoader:ImageLoader.Source="{Binding Favorites.CoverUrl}">
                    <Image.ContextMenu>
                        <ContextMenu>
                            <MenuItem Command="{Binding CopyCoverCommand}" Header="{DynamicResource CopyCover}" />
                            <MenuItem Command="{Binding CopyCoverUrlCommand}" Header="{DynamicResource CopyCoverUrl}" />
                            <!--  TODO 复制封面到文件  -->
                        </ContextMenu>
                    </Image.ContextMenu>
                </Image>

                <TextBlock
                    Margin="0,10,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    FontSize="18"
                    FontWeight="Bold"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{Binding Favorites.Title}"
                    TextTrimming="CharacterEllipsis"
                    ToolTip.Tip="{Binding Favorites.Title}" />

                <ScrollViewer
                    MaxHeight="80"
                    Margin="0,10,0,0"
                    VerticalScrollBarVisibility="Auto">
                    <TextBox
                        Background="{x:Null}"
                        BorderBrush="{x:Null}"
                        BorderThickness="0"
                        FontSize="12"
                        Foreground="{DynamicResource BrushTextDark}"
                        IsReadOnly="True"
                        Text="{Binding Favorites.Description}"
                        TextWrapping="WrapWithOverflow" />
                </ScrollViewer>

                <Grid Margin="0,15,0,0" ColumnDefinitions="*,*">

                    <TextBlock
                        Grid.Column="0"
                        HorizontalAlignment="Left"
                        FontSize="12"
                        Foreground="{DynamicResource BrushTextGrey}"
                        Text="{Binding Favorites.CreateTime}" />

                    <StackPanel
                        Grid.Column="1"
                        HorizontalAlignment="Right"
                        Orientation="Horizontal"
                        IsVisible="{Binding Favorites.MediaCount}">
                        <TextBlock
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextGrey}"
                            Text="{Binding Favorites.MediaCount}" />
                        <TextBlock
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextGrey}"
                            Text="{DynamicResource FavoritesMediaCount}" />
                    </StackPanel>
                </Grid>

                <Grid Margin="0,15,0,0" ColumnDefinitions="*,*,*,*">

                    <StackPanel
                        Grid.Column="0"
                        HorizontalAlignment="Center"
                        Orientation="Vertical">
                        <ContentControl Width="16" Height="16">
                            <Path
                                Width="{Binding Favorites.Play.Width}"
                                Height="{Binding Favorites.Play.Height}"
                                Data="{Binding Favorites.Play.Data}"
                                Fill="{Binding Favorites.Play.Fill}"
                                Stretch="UniformToFill" />
                        </ContentControl>
                        <TextBlock
                            Margin="0,5,0,0"
                            HorizontalAlignment="Center"
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextGrey2}"
                            Text="{Binding Favorites.PlayNumber}" />
                    </StackPanel>

                    <StackPanel
                        Grid.Column="1"
                        HorizontalAlignment="Center"
                        Orientation="Vertical">
                        <ContentControl Width="16" Height="16">
                            <Path
                                Width="{Binding Favorites.Like.Width}"
                                Height="{Binding Favorites.Like.Height}"
                                Data="{Binding Favorites.Like.Data}"
                                Fill="{Binding Favorites.Like.Fill}"
                                Stretch="UniformToFill" />
                        </ContentControl>
                        <TextBlock
                            Margin="0,5,0,0"
                            HorizontalAlignment="Center"
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextGrey2}"
                            Text="{Binding Favorites.LikeNumber}" />
                    </StackPanel>

                    <StackPanel
                        Grid.Column="2"
                        HorizontalAlignment="Center"
                        Orientation="Vertical">
                        <ContentControl Width="16" Height="16">
                            <Path
                                Width="{Binding Favorites.Favorite.Width}"
                                Height="{Binding Favorites.Favorite.Height}"
                                Data="{Binding Favorites.Favorite.Data}"
                                Fill="{Binding Favorites.Favorite.Fill}"
                                Stretch="UniformToFill" />
                        </ContentControl>
                        <TextBlock
                            Margin="0,5,0,0"
                            HorizontalAlignment="Center"
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextGrey2}"
                            Text="{Binding Favorites.FavoriteNumber}" />
                    </StackPanel>

                    <StackPanel
                        Grid.Column="3"
                        HorizontalAlignment="Center"
                        Orientation="Vertical">
                        <ContentControl Width="16" Height="16">
                            <Path
                                Width="{Binding Favorites.Share.Width}"
                                Height="{Binding Favorites.Share.Height}"
                                Data="{Binding Favorites.Share.Data}"
                                Fill="{Binding Favorites.Share.Fill}"
                                Stretch="UniformToFill" />
                        </ContentControl>
                        <TextBlock
                            Margin="0,5,0,0"
                            HorizontalAlignment="Center"
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextGrey2}"
                            Text="{Binding Favorites.ShareNumber}" />
                    </StackPanel>
                </Grid>

                <StackPanel
                    Margin="0,20,0,0"
                    Cursor="Hand"
                    Orientation="Horizontal"
                    ToolTip.Tip="{Binding Favorites.UpName}">
                    <Interaction.Behaviors>
                        <EventTriggerBehavior EventName="PointerReleased">
                            <InvokeCommandAction Command="{Binding UpperCommand}" />
                        </EventTriggerBehavior>
                    </Interaction.Behaviors>

                    <Image
                        Width="48"
                        Height="48"
                        asyncImageLoader:ImageLoader.Source="{Binding Favorites.UpHeader}">
                        <Image.Clip>
                            <EllipseGeometry
                                Center="24,24"
                                RadiusX="24"
                                RadiusY="24" />
                        </Image.Clip>
                    </Image>
                    <TextBlock
                        Margin="15,0,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="14"
                        Foreground="{DynamicResource BrushPrimary}"
                        Text="{Binding Favorites.UpName}"
                        TextTrimming="CharacterEllipsis" />
                </StackPanel>

                <TextBlock
                    Height="1"
                    Margin="0,20,0,0"
                    Background="{DynamicResource BrushBorder}" />

                <Button
                    Height="40"
                    Margin="0,20,0,0"
                    HorizontalAlignment="Stretch"
                    Command="{Binding AddToDownloadCommand}"
                    Content="{DynamicResource DownloadSelected}"
                    FontSize="12"
                    Foreground="{DynamicResource BrushText}" />

                <Button
                    Height="40"
                    Margin="0,20,0,0"
                    HorizontalAlignment="Stretch"
                    Command="{Binding AddAllToDownloadCommand}"
                    Content="{DynamicResource AddAllToDownload}"
                    FontSize="12"
                    Foreground="{DynamicResource BrushText}" />
            </StackPanel>


            <!--  右侧内容区  -->
            <ListBox
                x:Name="NameFavoritesMedias"
                Grid.Column="1"
                Margin="30,0,0,0"
                ItemsSource="{Binding FavoritesMedias, Mode=TwoWay}"
                SelectionMode="Multiple">
                <Interaction.Behaviors>
                    <EventTriggerBehavior EventName="SelectionChanged">
                        <InvokeCommandAction Command="{Binding FavoritesMediasCommand}"
                                             CommandParameter="{Binding ElementName=NameFavoritesMedias, Path=SelectedItem}" />
                    </EventTriggerBehavior>
                </Interaction.Behaviors>
                <ListBox.Template>
                    <ControlTemplate TargetType="{x:Type ListBox}">
                        <ScrollViewer
                            HorizontalScrollBarVisibility="Disabled"
                            VerticalScrollBarVisibility="Auto">
                            <StackPanel Orientation="Vertical">
                                <ItemsPresenter />
                            </StackPanel>
                        </ScrollViewer>
                    </ControlTemplate>
                </ListBox.Template>
                <ListBox.ItemContainerTheme>
                    <ControlTheme TargetType="{x:Type ListBoxItem}" x:DataType="vmp:FavoritesMedia">
                        <Setter Property="IsSelected" Value="{Binding IsSelected, Mode=TwoWay}" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="{x:Type ListBoxItem}">
                                    <Grid Margin="0,10" Background="{DynamicResource BrushBackground}"
                                          ColumnDefinitions="auto,150,*,auto">

                                        <TextBlock
                                            Grid.Column="0"
                                            Width="35"
                                            Margin="0,0,5,0"
                                            VerticalAlignment="Center"
                                            FontSize="14"
                                            Foreground="{DynamicResource BrushTextDark}"
                                            Text="{Binding Order}" />

                                        <Image Grid.Column="1" Source="{Binding Cover}" />

                                        <Border
                                            x:Name="nameInfoPanel"
                                            Grid.Column="1"
                                            Width="150"
                                            Height="95"
                                            Background="{DynamicResource BrushMask}"
                                            IsVisible="False">
                                            <TextBlock
                                                Margin="5,0,0,5"
                                                HorizontalAlignment="Left"
                                                VerticalAlignment="Bottom"
                                                FontSize="12"
                                                Foreground="{DynamicResource BrushText}"
                                                Text="{Binding Duration}" />
                                        </Border>

                                        <Image
                                            x:Name="nameIsSelected"
                                            Grid.Column="1"
                                            Width="24"
                                            Height="24"
                                            Margin="10"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Top"
                                            Source="/Resources/checked.png" />

                                        <Grid Grid.Column="2" Margin="20,0" RowDefinitions="*,auto,auto">

                                            <TextBlock
                                                Grid.Row="0"
                                                HorizontalAlignment="Left"
                                                VerticalAlignment="Top"
                                                Cursor="Hand"
                                                FontSize="14"
                                                Text="{Binding Title}"
                                                TextTrimming="CharacterEllipsis"
                                                ToolTip.Tip="{Binding Title}">
                                                <Interaction.Behaviors>
                                                    <EventTriggerBehavior EventName="PointerReleased">
                                                        <InvokeCommandAction Command="{Binding TitleCommand}"
                                                                             CommandParameter="{ReflectionBinding DataContext.PageName, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=UserControl}}" />
                                                    </EventTriggerBehavior>
                                                </Interaction.Behaviors>
                                                <TextBlock.Styles>
                                                    <Style Selector="TextBlock">
                                                        <Setter Property="Foreground"
                                                                Value="{DynamicResource BrushTextDark}" />
                                                        <Style Selector="^:pointerover">
                                                            <Setter Property="Foreground"
                                                                    Value="{DynamicResource BrushPrimary}" />
                                                        </Style>
                                                    </Style>
                                                </TextBlock.Styles>
                                            </TextBlock>

                                            <StackPanel Grid.Row="1" Orientation="Horizontal">
                                                <TextBlock
                                                    FontSize="12"
                                                    Foreground="{DynamicResource BrushTextGrey}"
                                                    Text="{Binding PlayNumber}" />
                                                <TextBlock
                                                    FontSize="12"
                                                    Foreground="{DynamicResource BrushTextGrey}"
                                                    Text="{DynamicResource Play}" />
                                                <TextBlock
                                                    FontSize="12"
                                                    Foreground="{DynamicResource BrushTextGrey}"
                                                    Text=" · " />

                                                <TextBlock
                                                    FontSize="12"
                                                    Foreground="{DynamicResource BrushTextGrey}"
                                                    Text="{Binding DanmakuNumber}" />
                                                <TextBlock
                                                    FontSize="12"
                                                    Foreground="{DynamicResource BrushTextGrey}"
                                                    Text="{DynamicResource Danmaku}" />
                                                <TextBlock
                                                    FontSize="12"
                                                    Foreground="{DynamicResource BrushTextGrey}"
                                                    Text=" · " />

                                                <TextBlock
                                                    FontSize="12"
                                                    Foreground="{DynamicResource BrushTextGrey}"
                                                    Text="{Binding FavoriteNumber}" />
                                                <TextBlock
                                                    FontSize="12"
                                                    Foreground="{DynamicResource BrushTextGrey}"
                                                    Text="{DynamicResource Favorite}" />
                                            </StackPanel>

                                            <TextBlock
                                                Grid.Row="2"
                                                HorizontalAlignment="Left"
                                                VerticalAlignment="Top"
                                                Cursor="Hand"
                                                FontSize="12"
                                                Text="{Binding UpName, StringFormat={}UP: {0}}">
                                                <Interaction.Behaviors>
                                                    <EventTriggerBehavior EventName="PointerReleased">
                                                        <InvokeCommandAction Command="{Binding VideoUpperCommand}"
                                                                             CommandParameter="{ReflectionBinding DataContext.PageName, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=UserControl}}" />
                                                    </EventTriggerBehavior>
                                                </Interaction.Behaviors>
                                                <TextBlock.Styles>
                                                    <Style Selector="TextBlock">
                                                        <Setter Property="Foreground"
                                                                Value="{DynamicResource BrushTextGrey}" />
                                                        <Style Selector="^:pointerover">
                                                            <Setter Property="Foreground"
                                                                    Value="{DynamicResource BrushPrimary}" />
                                                        </Style>
                                                    </Style>
                                                </TextBlock.Styles>
                                            </TextBlock>
                                        </Grid>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style Selector="^ /template/ Image#nameIsSelected">
                            <Setter Property="IsVisible" Value="False" />
                        </Style>
                        <Style Selector="^:selected /template/ Image#nameIsSelected">
                            <Setter Property="IsVisible" Value="True" />
                        </Style>
                        <Style Selector="^:pointerover /template/ Border#nameInfoPanel">
                            <Setter Property="IsVisible" Value="True" />
                        </Style>
                    </ControlTheme>
                </ListBox.ItemContainerTheme>
            </ListBox>

            <!--  加载gif  -->
            <StackPanel
                Grid.Column="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Orientation="Vertical"
                IsVisible="{Binding MediaLoadingVisibility}">
                <custom:Loading
                    Width="40"
                    Height="40"
                    Foreground="Gray"
                    IsActive="{Binding MediaLoading}" />
            </StackPanel>

        </Grid>
        <!--  加载gif  -->
        <StackPanel
            Grid.Row="2"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Orientation="Vertical"
            IsVisible="{Binding LoadingVisibility}">
            <custom:Loading
                Width="40"
                Height="40"
                Foreground="Gray"
                IsActive="{Binding Loading}" />
            <TextBlock
                Margin="0,10,0,0"
                FontSize="14"
                Foreground="{DynamicResource BrushTextDark}"
                Text="{DynamicResource PublicationWait}" />
        </StackPanel>

        <!--  没有数据提示  -->
        <Image
            Grid.Row="2"
            Width="256"
            Height="256"
            Source="/Resources/no-data.png"
            IsVisible="{Binding NoDataVisibility}" />
    </Grid>
</UserControl>