name: Build

permissions:
  contents: write

on:
  push:
    tags:
      - 'v*'

jobs:
  changelog:
    name: Generate changelog
    runs-on: ubuntu-latest
    outputs:
      release_body: ${{ steps.git-cliff.outputs.content }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Generate a changelog
        uses: orhun/git-cliff-action@v4
        id: git-cliff
        with:
          config: cliff.toml
          args: -vv --latest --strip header
        env:
          OUTPUT: CHANGELOG.md
      - name: Print the changelog
        run: cat "${{ steps.git-cliff.outputs.changelog }}"
  build-windows:
    runs-on: windows-latest
    needs: changelog
    strategy:
      matrix:
        cpu: [ x64, x86 ]
        kind: [ zip ]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '6.0.x'
      - name: Read Version Text
        id: version
        uses: juliangruber/read-file-action@v1
        with:
          path: version.txt
      - name: Install Pupnet
        run: |
          dotnet tool install -g KuiperZone.PupNet --version 1.7.1
      - name: Get Deps
        run: |
          .\ffmpeg.ps1 ${{ matrix.cpu }}
          .\aria2.ps1 ${{ matrix.cpu }}
        working-directory: ./script
      - name: Package
        run: |
          pupnet DownKyi.pupnet.conf --app-version ${{ steps.version.outputs.content }} --kind ${{ matrix.kind }} -r win-${{ matrix.cpu }} -y
        working-directory: ./script/pupnet
      - name: Upload build artifacts ${{ matrix.kind }}
        uses: actions/upload-artifact@v4
        with:
          name: DownKyi-${{ steps.version.outputs.content }}-1.win-${{ matrix.cpu }}.${{ matrix.kind }}
          path: script/pupnet/output/DownKyi-${{ steps.version.outputs.content }}-1.win-${{ matrix.cpu }}.${{ matrix.kind }}
      - name: Release
        uses: ncipollo/release-action@v1
        if: ${{ startsWith(github.ref, 'refs/tags/') }}
        with:
          allowUpdates: true
          artifacts: script/pupnet/output/DownKyi-${{ steps.version.outputs.content }}-1.win-${{ matrix.cpu }}.${{ matrix.kind }}
          body: ${{ needs.changelog.outputs.release_body }}
  build-linux:
    runs-on: ubuntu-22.04
    needs: changelog
    strategy:
      matrix:
        cpu: [ x64, arm64 ]
        kind: [ AppImage, deb, rpm ]
        type: [ self-contained ]
        include:
          - kind: AppImage
            cpu: x64
            package-tail: .x86_64
            package-head: DownKyi-
          - kind: deb
            cpu: x64
            package-tail: _amd64
            package-head: downkyi_
          - kind: rpm
            cpu: x64
            package-tail: .x86_64
            package-head: downkyi_
          - kind: AppImage
            cpu: arm64
            package-tail: .aarch64
            package-head: DownKyi-
          - kind: deb
            cpu: arm64
            package-tail: _arm64
            package-head: downkyi_
        exclude:
          - kind: rpm
            cpu: arm64
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '6.0.x'
      - name: Read Version Text
        id: version
        uses: juliangruber/read-file-action@v1
        with:
          path: version.txt
      - name: Install Pupnet
        run: |
          dotnet tool install -g KuiperZone.PupNet --version 1.7.1
          sudo apt update
          sudo apt install libfuse2
      - name: Get Deps
        run: |
          chmod +x ffmpeg.sh
          chmod +x aria2.sh
          ./ffmpeg.sh linux ${{ matrix.cpu }}
          ./aria2.sh linux-${{ matrix.cpu }}
        working-directory: ./script
      - name: Package
        run: |
          pupnet DownKyi.pupnet.conf --app-version ${{ steps.version.outputs.content }} --kind ${{ matrix.kind }} -r linux-${{ matrix.cpu }} -y
        working-directory: ./script/pupnet
      - name: Upload build artifacts ${{ matrix.kind }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.package-head }}${{ steps.version.outputs.content }}_linux_${{ matrix.type }}${{ matrix.package-tail }}.${{ matrix.kind }}
          path: script/pupnet/output/${{ matrix.package-head }}${{ steps.version.outputs.content }}-1${{ matrix.package-tail }}.${{ matrix.kind }}
      - name: Change Name
        if: ${{ startsWith(github.ref, 'refs/tags/') }}
        run: mv "${{ matrix.package-head }}${{ steps.version.outputs.content }}-1${{ matrix.package-tail }}.${{ matrix.kind }}" "${{ matrix.package-head }}${{ steps.version.outputs.content }}_linux_${{ matrix.type }}${{ matrix.package-tail }}.${{ matrix.kind }}"
        working-directory: ./script/pupnet/output/
      - name: Release
        uses: ncipollo/release-action@v1
        if: ${{ startsWith(github.ref, 'refs/tags/') }}
        with:
          allowUpdates: true
          artifacts: script/pupnet/output/${{ matrix.package-head }}${{ steps.version.outputs.content }}_linux_${{ matrix.type }}${{ matrix.package-tail }}.${{ matrix.kind }}
          body: ${{ needs.changelog.outputs.release_body }}
  build-macos:
    runs-on: macos-latest
    needs: changelog
    strategy:
      matrix:
        cpu: [ x64, arm64 ]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Import certificate
        uses: apple-actions/import-codesign-certs@v2
        with:
          p12-file-base64: ${{ secrets.MACOS_CERTIFICATE }}
          p12-password: ${{ secrets.MACOS_CERTIFICATE_PWD }}
      - name: Install Tool Via HomeBrew
        run: |
          export HOMEBREW_NO_AUTO_UPDATE=true
          export HOMEBREW_NO_INSTALLED_DEPENDENTS_CHECK=true
          brew install create-dmg tree
      - name: Install .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '6.0.x'
      - name: Read Version Text
        id: version
        uses: juliangruber/read-file-action@v1
        with:
          path: version.txt
      - name: Get Deps
        run: |
          chmod +x aria2.sh
          chmod +x ffmpeg.sh
          ./aria2.sh osx-${{ matrix.cpu }}
          ./ffmpeg.sh mac ${{ matrix.cpu }}
        working-directory: ./script
      - name: Build ${{ matrix.cpu }}
        run: |
          dotnet restore
          dotnet publish --self-contained -r osx-${{ matrix.cpu }} -c Release -p:DebugType=None -p:DebugSymbols=false
      - name: Package app
        run: |
          chmod +x package.sh
          ./package.sh ${{ matrix.cpu }}
        working-directory: ./script/macos
      - name: Sign app
        run: |
          chmod +x sign.sh
          ./sign.sh
        working-directory: ./script/macos
      - name: Notarize app
        run: |
          ditto -c -k --sequesterRsrc --keepParent 哔哩下载姬.app 哔哩下载姬.zip
          xcrun notarytool submit 哔哩下载姬.zip --apple-id "${{ secrets.APPLE_ID }}" --team-id "${{ secrets.TEAM_ID }}" --password "${{ secrets.APP_SPECIFIC_PASSWORD }}" --wait
          xcrun stapler staple 哔哩下载姬.app
          rm -rf ./哔哩下载姬.zip
        working-directory: ./script/macos
      - name: Create DMG
        run: |
          create-dmg \
          --hdiutil-quiet \
          --icon 哔哩下载姬.app 165 175 \
          --icon-size 120 \
          --app-drop-link 495 175 \
          --window-size 660 400 \
          DownKyi-${{ steps.version.outputs.content }}-osx-${{ matrix.cpu }}.dmg 哔哩下载姬.app
        working-directory: ./script/macos
      - name: Notarize DMG
        run: |
          xcrun notarytool submit DownKyi-${{ steps.version.outputs.content }}-osx-${{ matrix.cpu }}.dmg --apple-id "${{ secrets.APPLE_ID }}" --team-id "${{ secrets.TEAM_ID }}" --password "${{ secrets.APP_SPECIFIC_PASSWORD }}" --wait
          xcrun stapler staple DownKyi-${{ steps.version.outputs.content }}-osx-${{ matrix.cpu }}.dmg
        working-directory: ./script/macos
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: DownKyi-${{ steps.version.outputs.content }}-osx-${{ matrix.cpu }}.dmg
          path: script/macos/DownKyi-${{ steps.version.outputs.content }}-osx-${{ matrix.cpu }}.dmg
      - name: Release
        uses: ncipollo/release-action@v1
        if: ${{ startsWith(github.ref, 'refs/tags/') }}
        with:
          allowUpdates: true
          artifacts: script/macos/DownKyi-${{ steps.version.outputs.content }}-osx-${{ matrix.cpu }}.dmg
          body: ${{ needs.changelog.outputs.release_body }}
