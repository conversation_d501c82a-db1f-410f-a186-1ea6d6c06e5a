﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="DownKyi.Views.Toolbox.ViewBiliHelper"
             xmlns:vm="clr-namespace:DownKyi.ViewModels.Toolbox"
             x:DataType="vm:ViewBiliHelperViewModel">
    <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="50,0" Orientation="Vertical">
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    FontSize="18"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource BiliHelper}" />
            </StackPanel>

            <HeaderedContentControl
                Header="{DynamicResource AvidAndBvidConversion}"
                Background="LightGray"
                Foreground="{DynamicResource BrushTextDark}"
                Margin="0,20,0,0">
                <StackPanel Margin="10">
                    <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                VerticalAlignment="Center"
                                FontSize="12"
                                Foreground="{DynamicResource BrushTextDark}"
                                Text="{DynamicResource Avid}" />
                            <TextBox
                                x:Name="NameAvid"
                                Width="150"
                                Margin="10"
                                VerticalContentAlignment="Center"
                                Text="{Binding Avid, Mode=TwoWay}">
                                <Interaction.Behaviors>
                                    <EventTriggerBehavior EventName="TextChanged">
                                        <InvokeCommandAction Command="{Binding AvidCommand}"
                                                             CommandParameter="{Binding ElementName=NameAvid, Path=Text}" />
                                    </EventTriggerBehavior>
                                </Interaction.Behaviors>
                            </TextBox>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                VerticalAlignment="Center"
                                FontSize="12"
                                Foreground="{DynamicResource BrushTextDark}"
                                Text="{DynamicResource Bvid}" />
                            <TextBox
                                x:Name="NameBvid"
                                Width="150"
                                Margin="10"
                                VerticalContentAlignment="Center"
                                Text="{Binding Bvid, Mode=TwoWay}">
                                <Interaction.Behaviors>
                                    <EventTriggerBehavior EventName="TextChanged">
                                        <InvokeCommandAction Command="{Binding BvidCommand}"
                                                             CommandParameter="{Binding ElementName=NameBvid, Path=Text}" />
                                    </EventTriggerBehavior>
                                </Interaction.Behaviors>
                            </TextBox>
                        </StackPanel>

                        <Button
                            Width="75"
                            Margin="30,0,0,0"
                            VerticalAlignment="Center"
                            Command="{Binding GotoWebCommand}"
                            Content="{DynamicResource GotoWeb}"
                            FontSize="12"
                            Theme="{StaticResource BtnBorderStyle}" />
                    </StackPanel>

                    <StackPanel>
                        <TextBlock
                            VerticalAlignment="Center"
                            FontSize="12"
                            Foreground="{DynamicResource BrushTextGrey}"
                            Text="{DynamicResource AvidAndBvidConversionTip}" />
                    </StackPanel>
                </StackPanel>
            </HeaderedContentControl>
            <HeaderedContentControl
                Header="{DynamicResource Danmaku}"
                Background="LightGray"
                Foreground="{DynamicResource BrushTextDark}"
                Margin="0,20,0,0">
                <StackPanel Orientation="Horizontal" Margin="10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock VerticalAlignment="Center" Text="{DynamicResource DanmakuUserID}" />
                        <TextBox
                            Name="NameUserId"
                            Width="150"
                            Margin="10"
                            VerticalContentAlignment="Center"
                            Text="{Binding DanmakuUserId, Mode=TwoWay}" />
                    </StackPanel>

                    <Button
                        Margin="30,0,0,0"
                        VerticalAlignment="Center"
                        Command="{Binding FindDanmakuSenderCommand}"
                        Content="{DynamicResource FindDanmakuSender}"
                        FontSize="12"
                        Theme="{StaticResource BtnBorderStyle}" />

                    <StackPanel Width="100" Orientation="Horizontal">
                        <TextBox
                            Height="25"
                            Margin="10,0,0,0"
                            VerticalContentAlignment="Center"
                            BorderBrush="{x:Null}"
                            BorderThickness="0"
                            Cursor="Hand"
                            FontSize="12"
                            IsReadOnly="True"
                            Text="{Binding UserMid, Mode=TwoWay}">
                            <Interaction.Behaviors>
                                <EventTriggerBehavior EventName="PointerReleased">
                                    <InvokeCommandAction Command="{Binding VisitUserSpaceCommand}" />
                                </EventTriggerBehavior>
                            </Interaction.Behaviors>
                            <TextBox.Styles>
                                <Style Selector="TextBox">
                                    <Setter Property="Foreground" Value="{DynamicResource BrushTextDark}" />
                                    <Style Selector="^:pointerover">
                                        <Setter Property="Foreground" Value="{DynamicResource BrushPrimary}" />
                                    </Style>
                                </Style>
                            </TextBox.Styles>
                        </TextBox>
                    </StackPanel>

                </StackPanel>
            </HeaderedContentControl>
        </StackPanel>
    </ScrollViewer>
</UserControl>