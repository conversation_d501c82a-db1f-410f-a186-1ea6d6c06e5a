﻿using DownKyi.Core.BiliApi.Models;
using Newtonsoft.Json;

namespace DownKyi.Core.BiliApi.Users.Models;

public class SpaceChannelVideoList : BaseModel
{
    [JsonProperty("cid")] public long Cid { get; set; }
    [Json<PERSON>roperty("mid")] public long Mid { get; set; }
    [Json<PERSON>roperty("name")] public string Name { get; set; }
    [Json<PERSON>roperty("intro")] public string Intro { get; set; }
    [JsonProperty("mtime")] public long Mtime { get; set; }
    [JsonProperty("count")] public int Count { get; set; }

    [JsonProperty("cover")] public string Cover { get; set; }

    // is_live_playback
    [JsonProperty("archives")] public List<SpaceChannelArchive> Archives { get; set; }
}