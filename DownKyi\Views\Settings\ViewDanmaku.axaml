<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="DownKyi.Views.Settings.ViewDanmaku"
             xmlns:prism="http://prismlibrary.com/"
             prism:ViewModelLocator.AutoWireViewModel="True"
             xmlns:vms="clr-namespace:DownKyi.ViewModels.Settings"
             x:DataType="vms:ViewDanmakuViewModel">
    <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="50,0" Orientation="Vertical">
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    FontSize="18"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource SettingDanmaku}" />
            </StackPanel>

            <HeaderedContentControl
                Background="LightGray"
                Margin="0,20,0,0"
                Padding="20,5"
                HorizontalAlignment="Left"
                FontSize="12"
                Foreground="{DynamicResource BrushTextDark}"
                Header="{DynamicResource FilterType}">
                <StackPanel Margin="10" Orientation="Horizontal">
                    <CheckBox
                        Margin="0,0,0,0"
                        Command="{Binding TopFilterCommand}"
                        Content="{DynamicResource TopFilter}"
                        Foreground="{DynamicResource BrushTextDark}"
                        IsChecked="{Binding TopFilter, Mode=TwoWay}" />
                    <CheckBox
                        Margin="40,0,0,0"
                        Command="{Binding BottomFilterCommand}"
                        Content="{DynamicResource BottomFilter}"
                        Foreground="{DynamicResource BrushTextDark}"
                        IsChecked="{Binding BottomFilter, Mode=TwoWay}" />
                    <CheckBox
                        Margin="40,0,0,0"
                        Command="{Binding ScrollFilterCommand}"
                        Content="{DynamicResource ScrollFilter}"
                        Foreground="{DynamicResource BrushTextDark}"
                        IsChecked="{Binding ScrollFilter, Mode=TwoWay}" />
                </StackPanel>
            </HeaderedContentControl>


            <StackPanel
                Margin="0,20,0,0"
                Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource Resolution}" />
                <TextBox
                    Name="NameScreenWidth"
                    Width="95"
                    VerticalContentAlignment="Center"
                    Text="{Binding ScreenWidth, Mode=TwoWay}">
                    <Interaction.Behaviors>
                        <ExecuteCommandOnKeyDownBehavior
                            Key="Enter"
                            Command="{Binding ScreenWidthCommand}"
                            CommandParameter="{Binding ElementName=NameScreenWidth, Path=Text}" />
                    </Interaction.Behaviors>
                </TextBox>
                <TextBlock
                    Margin="5,0"
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="×" />
                <TextBox
                    Name="NameScreenHeight"
                    Width="95"
                    VerticalContentAlignment="Center"
                    Text="{Binding ScreenHeight, Mode=TwoWay}">
                    <Interaction.Behaviors>
                        <ExecuteCommandOnKeyDownBehavior
                            Key="Enter"
                            Command="{Binding ScreenHeightCommand}"
                            CommandParameter="{Binding ElementName=NameScreenHeight, Path=Text}" />
                    </Interaction.Behaviors>
                </TextBox>
            </StackPanel>
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource FontName}" />
                <ComboBox
                    Name="NameFonts"
                    Width="200"
                    VerticalContentAlignment="Center"
                    ItemsSource="{Binding Fonts}"
                    SelectedValue="{Binding SelectedFont}">
                    <Interaction.Behaviors>
                        <EventTriggerBehavior EventName="SelectionChanged">
                            <InvokeCommandAction
                                Command="{Binding FontSelectCommand}"
                                CommandParameter="{Binding ElementName=NameFonts, Path=SelectedValue}" />
                        </EventTriggerBehavior>
                    </Interaction.Behaviors>

                </ComboBox>
            </StackPanel>
            <StackPanel
                Margin="0,20,0,0"
                Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource FontSize}" />
                <TextBox
                    x:Name="NameFontSize"
                    Width="200"
                    VerticalContentAlignment="Center"
                    Text="{Binding FontSize}">
                    <Interaction.Behaviors>
                        <ExecuteCommandOnKeyDownBehavior
                            Key="Enter"
                            Command="{Binding FontSizeCommand}"
                            CommandParameter="{Binding ElementName=NameFontSize, Path=Text}" />
                    </Interaction.Behaviors>
                </TextBox>
            </StackPanel>
            <StackPanel
                Margin="0,20,0,0"
                Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource LineCount}" />
                <TextBox
                    Name="NameLineCount"
                    Width="200"
                    VerticalContentAlignment="Center"
                    Text="{Binding LineCount}">
                    <Interaction.Behaviors>
                        <ExecuteCommandOnKeyDownBehavior
                            Key="Enter"
                            Command="{Binding LineCountCommand}"
                            CommandParameter="{Binding ElementName=NameLineCount, Path=Text}" />
                    </Interaction.Behaviors>
                </TextBox>
            </StackPanel>
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource LayoutAlgorithm}" />
                <CheckBox
                    HorizontalAlignment="Left"
                    VerticalAlignment="Top"
                    Command="{Binding LayoutAlgorithmCommand}"
                    CommandParameter="Sync"
                    Content="{DynamicResource LayoutAlgorithmSync}"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    IsChecked="{Binding Sync, Mode=TwoWay}" />
                <CheckBox
                    Margin="20,0,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Top"
                    Command="{Binding LayoutAlgorithmCommand}"
                    CommandParameter="Async"
                    Content="{DynamicResource LayoutAlgorithmAsync}"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    IsChecked="{Binding Async, Mode=TwoWay}" />
            </StackPanel>

            <StackPanel Margin="10" />
        </StackPanel>

    </ScrollViewer>
</UserControl>