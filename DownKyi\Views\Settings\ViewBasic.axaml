﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="DownKyi.Views.Settings.ViewBasic"
             xmlns:prism="http://prismlibrary.com/"
             prism:ViewModelLocator.AutoWireViewModel="True"
             xmlns:vms="clr-namespace:DownKyi.ViewModels.Settings"
             x:DataType="vms:ViewBasicViewModel">
    <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="50,0" Orientation="Vertical">
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    FontSize="18"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource Basic}" />
            </StackPanel>

            <StackPanel Margin="0,20,0,0" Orientation="Vertical" IsVisible="False">
                <TextBlock
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource AfterDownloadOperation}" />
                <StackPanel Margin="0,10,0,0" Orientation="Horizontal">
                    <RadioButton
                        Command="{Binding AfterDownloadOperationCommand}"
                        CommandParameter="None"
                        GroupName="AfterDownloadOperation"
                        Foreground="{DynamicResource BrushTextDark}"
                        FontSize="12"
                        Content="{StaticResource AfterDownloadOperationNone}"
                        IsChecked="{Binding None}"
                        Theme="{StaticResource RadioStyle}" />
                    <RadioButton
                        Margin="20,0,0,0"
                        GroupName="AfterDownloadOperation"
                        Foreground="{DynamicResource BrushTextDark}"
                        FontSize="12"
                        Command="{Binding AfterDownloadOperationCommand}"
                        CommandParameter="CloseApp"
                        IsChecked="{Binding CloseApp}"
                        Content="{StaticResource AfterDownloadOperationCloseApp}"
                        Theme="{StaticResource RadioStyle}" />
                    <RadioButton
                        Margin="20,0,0,0"
                        GroupName="AfterDownloadOperation"
                        Foreground="{DynamicResource BrushTextDark}"
                        FontSize="12"
                        Command="{Binding AfterDownloadOperationCommand}"
                        CommandParameter="CloseSystem"
                        IsChecked="{Binding CloseSystem}"
                        Content="{StaticResource AfterDownloadOperationCloseSystem}"
                        Theme="{StaticResource RadioStyle}" />
                </StackPanel>
            </StackPanel>
            <StackPanel Margin="0,20,0,0" Orientation="Vertical">
                <TextBlock
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource Theme}" />
                <StackPanel Margin="0,10,0,0" Orientation="Horizontal">
                    <RadioButton
                        Command="{Binding ThemeCommand}"
                        CommandParameter="Light"
                        GroupName="Theme"
                        Foreground="{DynamicResource BrushTextDark}"
                        FontSize="12"
                        Content="{StaticResource ThemeLight}"
                        IsChecked="{Binding ThemeLight}" />
                    <RadioButton
                        Margin="20,0,0,0"
                        GroupName="Theme"
                        Foreground="{DynamicResource BrushTextDark}"
                        FontSize="12"
                        Command="{Binding ThemeCommand}"
                        CommandParameter="Dark"
                        IsChecked="{Binding ThemeDark}"
                        Content="{StaticResource ThemeDark}" />
                    <RadioButton
                        Margin="20,0,0,0"
                        GroupName="Theme"
                        Foreground="{DynamicResource BrushTextDark}"
                        FontSize="12"
                        Command="{Binding ThemeCommand}"
                        CommandParameter="Auto"
                        IsChecked="{Binding ThemeAuto}"
                        Content="{StaticResource ThemeAuto}" />
                </StackPanel>
            </StackPanel>
            <TextBlock
                Height="1"
                Margin="0,20,0,0"
                Background="{DynamicResource BrushBorder}" />
            <CheckBox
                Margin="0,20,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Command="{Binding ListenClipboardCommand}"
                Content="{DynamicResource ListenClipboard}"
                Foreground="{DynamicResource BrushTextDark}"
                IsChecked="{Binding ListenClipboard, Mode=TwoWay}" />
            <CheckBox
                Margin="0,20,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Command="{Binding AutoParseVideoCommand}"
                Content="{DynamicResource VideoAutoParse}"
                Foreground="{DynamicResource BrushTextDark}"
                IsChecked="{Binding AutoParseVideo, Mode=TwoWay}" />

            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource VideoParseScope}" />
                <ComboBox
                    Name="NameParseScopes"
                    Width="120"
                    VerticalContentAlignment="Center"
                    DisplayMemberBinding="{Binding Name}"
                    ItemsSource="{Binding ParseScopes}"
                    SelectedItem="{Binding SelectedParseScope}">
                    <Interaction.Behaviors>
                        <EventTriggerBehavior EventName="SelectionChanged">
                            <InvokeCommandAction Command="{Binding ParseScopesCommand}"
                                                 CommandParameter="{Binding ElementName=NameParseScopes, Path=SelectedItem}" />
                        </EventTriggerBehavior>
                    </Interaction.Behaviors>

                </ComboBox>
            </StackPanel>
            <CheckBox
                Margin="0,20,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Command="{Binding AutoDownloadAllCommand}"
                Content="{DynamicResource AutoDownloadAll}"
                Foreground="{DynamicResource BrushTextDark}"
                IsChecked="{Binding AutoDownloadAll, Mode=TwoWay}" />
            <StackPanel Margin="0,20,0,0" Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource BrushTextDark}"
                    Text="{DynamicResource RepeatDownloadStrategy}" />
                <ComboBox
                    Name="NameRepeatDownloadStrategy"
                    Width="120"
                    VerticalContentAlignment="Center"
                    DisplayMemberBinding="{Binding Name}"
                    ItemsSource="{Binding RepeatDownloadStrategy}"
                    SelectedItem="{Binding SelectedRepeatDownloadStrategy}">
                    <Interaction.Behaviors>
                        <EventTriggerBehavior EventName="SelectionChanged">
                            <InvokeCommandAction Command="{Binding RepeatDownloadStrategyCommand}"
                                                 CommandParameter="{Binding ElementName=NameRepeatDownloadStrategy, Path=SelectedItem}" />
                        </EventTriggerBehavior>
                    </Interaction.Behaviors>

                </ComboBox>
            </StackPanel>
            <CheckBox
                Margin="0,20,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Command="{Binding RepeatFileAutoAddNumberSuffixCommand}"
                Content="{DynamicResource RepeatFileAutoAddNumberSuffix}"
                Foreground="{DynamicResource BrushTextDark}"
                IsChecked="{Binding RepeatFileAutoAddNumberSuffix, Mode=TwoWay}" />
            <StackPanel Margin="10" />
        </StackPanel>
    </ScrollViewer>
</UserControl>