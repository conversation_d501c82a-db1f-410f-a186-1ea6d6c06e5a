﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="DownKyi.Views.ViewMyBangumiFollow"
             xmlns:prism="http://prismlibrary.com/"
             prism:ViewModelLocator.AutoWireViewModel="True"
             xmlns:vm="clr-namespace:DownKyi.ViewModels"
             xmlns:vmp="clr-namespace:DownKyi.ViewModels.PageViewModels"
             xmlns:custom="clr-namespace:DownKyi.CustomControl"
             x:DataType="vm:ViewMyBangumiFollowViewModel"
             xmlns:asyncImageLoader="clr-namespace:DownKyi.CustomControl.AsyncImageLoader">
    <UserControl.Resources>
        <ControlTheme x:Key="MediaListStyle" TargetType="{x:Type ListBoxItem}" x:DataType="vmp:BangumiFollowMedia">
            <Setter Property="IsSelected" Value="{Binding IsSelected}" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ListBoxItem}">
                        <Grid
                            Name="nameMediaPanel"
                            Width="308"
                            Height="140"
                            Margin="25,15,10,5"
                            ColumnDefinitions="110,*">
                            <Border
                                Name="NameCover"
                                Grid.Column="0"
                                Width="110"
                                Height="140"
                                HorizontalAlignment="Center"
                                CornerRadius="5">
                                <Border.Background>
                                    <!--  长宽比：1.6  -->
                                    <ImageBrush asyncImageLoader:ImageBrushLoader.Source="{Binding Cover}"
                                                asyncImageLoader:ImageBrushLoader.Width="110"
                                                asyncImageLoader:ImageBrushLoader.Height="140" />
                                </Border.Background>
                            </Border>

                            <Image
                                Name="NameChecked"
                                Grid.Column="0"
                                Width="24"
                                Height="24"
                                Margin="10"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top"
                                IsVisible="False"
                                Source="/Resources/checked.png" />

                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock
                                    Name="nameTitle"
                                    Cursor="Hand"
                                    FontSize="16"
                                    Foreground="{DynamicResource BrushTextDark}"
                                    Text="{Binding Title}"
                                    TextTrimming="CharacterEllipsis"
                                    ToolTip.Tip="{Binding Title}">
                                    <Interaction.Behaviors>
                                        <EventTriggerBehavior EventName="PointerPressed">
                                            <InvokeCommandAction Command="{Binding TitleCommand}"
                                                                 CommandParameter="{ReflectionBinding DataContext.PageName, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=UserControl}}" />
                                        </EventTriggerBehavior>
                                    </Interaction.Behaviors>
                                </TextBlock>

                                <TextBlock
                                    Height="40"
                                    Margin="0,10,0,0"
                                    Foreground="{DynamicResource BrushTextDark}"
                                    Text="{Binding Evaluate}"
                                    TextTrimming="CharacterEllipsis"
                                    TextWrapping="WrapWithOverflow"
                                    ToolTip.Tip="{Binding Evaluate}" />

                                <StackPanel Margin="0,5,0,0" Orientation="Horizontal">
                                    <TextBlock Foreground="{DynamicResource BrushTextGrey}"
                                               Text="{Binding SeasonTypeName}" />
                                    <TextBlock
                                        Margin="5,0"
                                        Foreground="{DynamicResource BrushTextGrey}"
                                        Text="|" />
                                    <TextBlock Foreground="{DynamicResource BrushTextGrey}" Text="{Binding Area}" />
                                </StackPanel>

                                <StackPanel Margin="0,5,0,0" Orientation="Horizontal">
                                    <TextBlock Foreground="{DynamicResource BrushTextGrey}" Text="{Binding Progress}" />
                                    <TextBlock
                                        Margin="5,0"
                                        Foreground="{DynamicResource BrushTextGrey}"
                                        Text="|" />
                                    <TextBlock Foreground="{DynamicResource BrushTextGrey}" Text="{Binding IndexShow}" />
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style Selector="^ /template/ Image#nameChecked">
                <Setter Property="IsVisible" Value="True" />
            </Style>
            <Style Selector="^ /template/ TextBlock#nameTitle:pointerover">
                <Setter Property="Foreground" Value="{DynamicResource BrushPrimary}" />
            </Style>
        </ControlTheme>
    </UserControl.Resources>
    <Grid RowDefinitions="50,1,*">
        <Grid Grid.Row="0" ColumnDefinitions="100,*,100">
            <Button
                Grid.Column="0"
                Margin="10,5,0,5"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Command="{Binding BackSpaceCommand}"
                Theme="{StaticResource ImageBtnStyle}">
                <StackPanel Orientation="Horizontal">
                    <ContentControl Width="24" Height="24">
                        <Path
                            Width="{Binding ArrowBack.Width}"
                            Height="{Binding ArrowBack.Height}"
                            Data="{Binding ArrowBack.Data}"
                            Fill="{Binding ArrowBack.Fill}"
                            Stretch="None" />
                    </ContentControl>
                    <TextBlock
                        VerticalAlignment="Center"
                        FontSize="16"
                        Foreground="{DynamicResource BrushTextDark}"
                        Text="{DynamicResource MyBangumiFollow}" />
                </StackPanel>
            </Button>

            <!--  顶部Tab  -->
            <ListBox
                Name="NameTabHeaders"
                Grid.Column="1"
                BorderThickness="0"
                IsEnabled="{Binding IsEnabled}"
                ItemsSource="{Binding TabHeaders}"
                ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                ScrollViewer.VerticalScrollBarVisibility="Disabled"
                SelectedIndex="{Binding SelectTabId, Mode=TwoWay}">
                <Interaction.Behaviors>
                    <EventTriggerBehavior EventName="SelectionChanged">
                        <InvokeCommandAction Command="{Binding TabHeadersCommand}"
                                             CommandParameter="{Binding ElementName=NameTabHeaders, Path=SelectedItem}" />
                    </EventTriggerBehavior>
                </Interaction.Behaviors>
                <ListBox.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Orientation="Horizontal" />
                    </ItemsPanelTemplate>
                </ListBox.ItemsPanel>
                <ListBox.ItemContainerTheme>
                    <ControlTheme TargetType="{x:Type ListBoxItem}" x:DataType="vmp:BangumiFollowMedia">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="{x:Type ListBoxItem}">
                                    <StackPanel
                                        x:Name="panel"
                                        Margin="10,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Cursor="Hand">
                                        <TextBlock
                                            Name="nameText"
                                            Padding="0,0,0,3"
                                            Foreground="{DynamicResource BrushTextDark}"
                                            Text="{ReflectionBinding Title}" />
                                        <TextBlock Name="nameIndicator" Height="1.5" IsVisible="False"
                                                   Background="{DynamicResource BrushTextDark}" />
                                    </StackPanel>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style Selector="^:pointerover /template/ TextBlock#nameText">
                            <Setter Property="Foreground" Value="{DynamicResource BrushPrimary}" />
                        </Style>
                        <Style Selector="^:selected /template/ TextBlock#nameText">
                            <Setter Property="Foreground" Value="{DynamicResource BrushPrimary}" />
                        </Style>
                        <Style Selector="^:selected /template/ TextBlock#nameIndicator">
                            <Setter Property="IsVisible" Value="True" />
                            <Setter Property="Background" Value="{DynamicResource BrushPrimary}" />
                        </Style>
                    </ControlTheme>
                </ListBox.ItemContainerTheme>
                <ListBox.Theme>
                    <ControlTheme TargetType="ListBox">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ListBox">
                                    <Border
                                        x:Name="Bd"
                                        Padding="0"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}">
                                        <ScrollViewer Focusable="False">
                                            <ItemsPresenter ItemsPanel="{TemplateBinding ItemsPanel}" />
                                        </ScrollViewer>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </ControlTheme>
                </ListBox.Theme>
            </ListBox>

            <Button
                Grid.Column="2"
                Width="24"
                Height="24"
                Margin="10,5"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Command="{Binding DownloadManagerCommand}"
                Theme="{StaticResource ImageBtnStyle}"
                ToolTip.Tip="{DynamicResource DownloadManager}">
                <ContentControl>
                    <Path
                        Width="{Binding DownloadManage.Width}"
                        Height="{Binding DownloadManage.Height}"
                        Data="{Binding DownloadManage.Data}"
                        Fill="{Binding DownloadManage.Fill}"
                        Stretch="Uniform" />
                </ContentControl>
            </Button>

        </Grid>

        <TextBlock Grid.Row="1" Background="{DynamicResource BrushBorder}" />

        <Grid Grid.Row="2" IsVisible="{Binding ContentVisibility}" RowDefinitions="*,1,50">
            <ListBox
                x:Name="NameMedias"
                Grid.Row="0"
                BorderThickness="0"
                ItemContainerTheme="{StaticResource MediaListStyle}"
                ItemsSource="{Binding Medias}"
                ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                SelectionMode="Multiple">
                <Interaction.Behaviors>
                    <EventTriggerBehavior EventName="SelectionChanged">
                        <InvokeCommandAction Command="{Binding MediasCommand}"
                                             CommandParameter="{Binding ElementName=NameMedias, Path=SelectedItems}" />
                    </EventTriggerBehavior>
                </Interaction.Behaviors>
                <ListBox.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel />
                    </ItemsPanelTemplate>
                </ListBox.ItemsPanel>
                <ListBox.Theme>
                    <ControlTheme TargetType="ListBox">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ListBox">
                                    <Border
                                        x:Name="Bd"
                                        Padding="0"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}">
                                        <ScrollViewer Focusable="False">
                                            <ItemsPresenter ItemsPanel="{TemplateBinding ItemsPanel}" />
                                        </ScrollViewer>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </ControlTheme>
                </ListBox.Theme>
            </ListBox>

            <TextBlock Grid.Row="1" Background="{DynamicResource BrushBorder}" />
            <Grid Grid.Row="2" ColumnDefinitions="80,*,100,90">

                <CheckBox
                    Grid.Column="0"
                    Margin="10,0,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Command="{Binding SelectAllCommand}"
                    CommandParameter="{Binding ElementName=NameMedias, Path=SelectedItem}"
                    Content="{DynamicResource SelectAll}"
                    Foreground="{DynamicResource BrushTextDark}"
                    IsChecked="{Binding IsSelectAll, Mode=TwoWay}" />

                <custom:CustomPager
                    Grid.Column="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    DataContext="{Binding Pager}" />

                <Button
                    Grid.Column="2"
                    Margin="0,0,10,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Command="{Binding AddToDownloadCommand}"
                    Content="{DynamicResource DownloadSelectedPublication}"
                    FontSize="12"
                    Foreground="{DynamicResource BrushText}"
                    Theme="{StaticResource BtnStyle}" />

                <Button
                    Grid.Column="3"
                    Margin="0,0,10,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Command="{Binding AddAllToDownloadCommand}"
                    Content="{DynamicResource DownloadAllPublication}"
                    FontSize="12"
                    Foreground="{DynamicResource BrushText}"
                    Theme="{StaticResource BtnStyle}" />
            </Grid>
        </Grid>
        <!--  加载gif  -->
        <StackPanel
            Grid.Row="2"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Orientation="Vertical"
            IsVisible="{Binding LoadingVisibility}">
            <custom:Loading
                Width="40"
                Height="40"
                Foreground="Gray"
                IsActive="{Binding Loading}" />
            <TextBlock
                Margin="0,10,0,0"
                FontSize="14"
                Foreground="{DynamicResource BrushTextDark}"
                Text="{DynamicResource PublicationWait}" />
        </StackPanel>

        <!--  没有数据提示  -->
        <Image
            Grid.Row="2"
            Width="256"
            Height="256"
            Source="/Resources/no-data.png"
            IsVisible="{Binding NoDataVisibility}" />
    </Grid>
</UserControl>